"""
Minimal Session-Aware Timeframe Converter for testing Phase 5.1.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta, time
from typing import Dict, Any, Tuple

# Handle imports for both module and direct execution
try:
    from ..config.minimal_settings import MinimalSettings
except ImportError:
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent))
    from config.minimal_settings import MinimalSettings


class MinimalSessionConverter:
    """
    Minimal session-aware timeframe converter for testing.
    """
    
    def __init__(self):
        self.settings = MinimalSettings()
        self.cache = {}
    
    def get_session_boundaries(self, pair: str, date: datetime) -> Dict[str, Any]:
        """Get market session boundaries for specific pair and date."""
        is_forex = self.settings.is_forex_pair(pair)
        market_open_str = self.settings.get_market_open_time(pair)
        
        # Parse market open time
        market_open_hour, market_open_minute = map(int, market_open_str.split(':'))
        market_open_time = time(market_open_hour, market_open_minute)
        
        # Calculate session boundaries for the date
        session_start = datetime.combine(date.date(), market_open_time)
        
        # H4 session boundaries
        h4_sessions = []
        current_time = session_start
        
        for i in range(6):  # Maximum 6 H4 sessions per day
            session_end = current_time + timedelta(hours=4)
            
            # Last session might be shorter (until market close)
            if i == 5:  # Last session
                next_day_start = session_start + timedelta(days=1)
                session_end = min(session_end, next_day_start)
            
            h4_sessions.append({
                'start': current_time,
                'end': session_end,
                'session_number': i + 1
            })
            
            current_time = session_end
        
        return {
            'pair': pair,
            'date': date,
            'is_forex': is_forex,
            'market_open': market_open_str,
            'session_start': session_start,
            'h4_sessions': h4_sessions,
            'daily_start': session_start,
            'daily_end': session_start + timedelta(days=1)
        }
    
    def find_actual_session_boundaries(self, m1_data: pd.DataFrame, 
                                     ideal_start: datetime, 
                                     ideal_end: datetime) -> Tuple[datetime, datetime]:
        """Find actual session boundaries based on available M1 data."""
        if m1_data.empty:
            return ideal_start, ideal_end
        
        # Filter data within ideal session range
        session_mask = (m1_data.index >= ideal_start) & (m1_data.index < ideal_end)
        session_data = m1_data[session_mask]
        
        if session_data.empty:
            return ideal_start, ideal_end
        
        # Use actual first and last available data points
        actual_start = session_data.index[0]
        actual_end = session_data.index[-1] + timedelta(minutes=1)  # Add 1 minute for inclusive end
        
        return actual_start, actual_end
    
    def convert_to_h4(self, m1_data: pd.DataFrame, pair: str) -> pd.DataFrame:
        """Convert M1 data to H4 using session boundaries."""
        if m1_data.empty:
            return pd.DataFrame()
        
        h4_candles = []
        
        # Group data by date
        m1_data_by_date = m1_data.groupby(m1_data.index.date)
        
        for date, daily_data in m1_data_by_date:
            date_obj = datetime.combine(date, time.min)
            session_info = self.get_session_boundaries(pair, date_obj)
            
            # Process each H4 session
            for session in session_info['h4_sessions']:
                ideal_start = session['start']
                ideal_end = session['end']
                
                # Find actual boundaries based on available data
                actual_start, actual_end = self.find_actual_session_boundaries(
                    daily_data, ideal_start, ideal_end
                )
                
                # Filter data for this session
                session_mask = (daily_data.index >= actual_start) & (daily_data.index < actual_end)
                session_data = daily_data[session_mask]
                
                if not session_data.empty:
                    # Create H4 candle
                    h4_candle = self._create_ohlcv_candle(session_data, ideal_start)
                    if h4_candle:
                        h4_candles.append(h4_candle)
        
        if not h4_candles:
            return pd.DataFrame()
        
        # Create H4 DataFrame
        h4_df = pd.DataFrame(h4_candles)
        h4_df.set_index('timestamp', inplace=True)
        h4_df.sort_index(inplace=True)
        
        return h4_df
    
    def _create_ohlcv_candle(self, session_data: pd.DataFrame, timestamp: datetime) -> Dict[str, Any]:
        """Create OHLCV candle from session data."""
        if session_data.empty:
            return None
        
        return {
            'timestamp': timestamp,
            'open': session_data['open'].iloc[0],
            'high': session_data['high'].max(),
            'low': session_data['low'].min(),
            'close': session_data['close'].iloc[-1],
            'volume': session_data['volume'].sum() if 'volume' in session_data.columns else 0
        }


# Test the converter
if __name__ == "__main__":
    print("Testing MinimalSessionConverter...")
    
    converter = MinimalSessionConverter()
    print("✓ Converter created")
    
    # Test session boundaries for forex
    date = datetime(2024, 1, 15)  # Monday
    forex_sessions = converter.get_session_boundaries('EURUSD', date)
    print(f"✓ Forex sessions: {len(forex_sessions['h4_sessions'])} H4 sessions")
    print(f"  Market open: {forex_sessions['market_open']}")
    print(f"  First session: {forex_sessions['h4_sessions'][0]['start']} - {forex_sessions['h4_sessions'][0]['end']}")
    
    # Test session boundaries for non-forex
    gold_sessions = converter.get_session_boundaries('XAUUSD', date)
    print(f"✓ Gold sessions: {len(gold_sessions['h4_sessions'])} H4 sessions")
    print(f"  Market open: {gold_sessions['market_open']}")
    print(f"  First session: {gold_sessions['h4_sessions'][0]['start']} - {gold_sessions['h4_sessions'][0]['end']}")
    
    # Test with sample data
    start_time = datetime(2024, 1, 15, 16, 0)  # Monday 16:00
    times = [start_time + timedelta(minutes=i) for i in range(240)]  # 4 hours of M1 data
    
    sample_data = pd.DataFrame({
        'open': np.random.uniform(1.0800, 1.0900, len(times)),
        'high': np.random.uniform(1.0850, 1.0950, len(times)),
        'low': np.random.uniform(1.0750, 1.0850, len(times)),
        'close': np.random.uniform(1.0800, 1.0900, len(times)),
        'volume': np.random.randint(100, 1000, len(times))
    }, index=pd.DatetimeIndex(times))
    
    # Fix OHLC logic
    for i in range(len(sample_data)):
        row = sample_data.iloc[i]
        high = max(row['open'], row['close']) + np.random.uniform(0, 0.01)
        low = min(row['open'], row['close']) - np.random.uniform(0, 0.01)
        sample_data.iloc[i, sample_data.columns.get_loc('high')] = high
        sample_data.iloc[i, sample_data.columns.get_loc('low')] = low
    
    print(f"✓ Created sample M1 data: {len(sample_data)} candles")
    
    # Convert to H4
    h4_data = converter.convert_to_h4(sample_data, 'EURUSD')
    print(f"✓ Converted to H4: {len(h4_data)} candles")
    
    if not h4_data.empty:
        h4_candle = h4_data.iloc[0]
        print(f"  H4 candle: O={h4_candle['open']:.4f} H={h4_candle['high']:.4f} L={h4_candle['low']:.4f} C={h4_candle['close']:.4f}")
    
    print("✅ All tests passed!")
