"""
Test settings save functionality.
"""

import json
from pathlib import Path
from datetime import datetime

def test_settings_save():
    """Test that settings can be saved to JSON file."""
    print("🧪 Testing Settings Save Functionality")
    print("=" * 40)
    
    # Get project directory
    project_dir = Path(__file__).parent.parent
    config_dir = project_dir / 'config'
    settings_file = config_dir / 'user_settings.json'
    
    print(f"Project dir: {project_dir}")
    print(f"Config dir: {config_dir}")
    print(f"Settings file: {settings_file}")
    
    # Ensure config directory exists
    config_dir.mkdir(exist_ok=True)
    print("✓ Config directory created/exists")
    
    # Test settings data
    test_settings = {
        'version': '1.0.0',
        'created': datetime.now().isoformat(),
        'timezone': {
            'data_timezone': 'UTC-5',
            'display_timezone': 'UTC+7'
        },
        'market_sessions': {
            'forex_open': '16:00',
            'non_forex_open': '17:00',
            'non_forex_symbols': ['XAUUSD', 'SPXUSD', 'NSXUSD']
        },
        'data_loading': {
            'days_back_default': 5,
            'max_candles_load': 200000,
            'max_candles_display': 15000,
            'enabled_timeframes': ['M1', 'M5', 'M15', 'H1', 'H4', 'D1']
        },
        'cache': {
            'max_cache_size_gb': 10,
            'cache_compression': 'snappy',
            'enable_disk_cache': True
        },
        'ui_preferences': {
            'default_timeframe': 'H1',
            'chart_style': 'candlestick',
            'remove_gaps': True,
            'crosshair_vertical': True,
            'crosshair_horizontal': True
        },
        'last_updated': datetime.now().isoformat()
    }
    
    # Test 1: Save settings
    try:
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(test_settings, f, indent=2, ensure_ascii=False)
        print("✓ Settings saved to file")
    except Exception as e:
        print(f"❌ Failed to save settings: {e}")
        return False
    
    # Test 2: Read settings back
    try:
        with open(settings_file, 'r', encoding='utf-8') as f:
            loaded_settings = json.load(f)
        print("✓ Settings loaded from file")
    except Exception as e:
        print(f"❌ Failed to load settings: {e}")
        return False
    
    # Test 3: Verify content
    try:
        assert loaded_settings['timezone']['data_timezone'] == 'UTC-5'
        assert loaded_settings['market_sessions']['forex_open'] == '16:00'
        assert loaded_settings['data_loading']['days_back_default'] == 5
        assert loaded_settings['cache']['max_cache_size_gb'] == 10
        assert loaded_settings['ui_preferences']['default_timeframe'] == 'H1'
        print("✓ Settings content verified")
    except AssertionError as e:
        print(f"❌ Settings content mismatch: {e}")
        return False
    
    # Test 4: Update settings (simulate user change)
    try:
        loaded_settings['timezone']['data_timezone'] = 'UTC-8'
        loaded_settings['data_loading']['days_back_default'] = 10
        loaded_settings['last_updated'] = datetime.now().isoformat()
        
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(loaded_settings, f, indent=2, ensure_ascii=False)
        
        # Read back updated settings
        with open(settings_file, 'r', encoding='utf-8') as f:
            updated_settings = json.load(f)
        
        assert updated_settings['timezone']['data_timezone'] == 'UTC-8'
        assert updated_settings['data_loading']['days_back_default'] == 10
        print("✓ Settings update and persistence verified")
    except Exception as e:
        print(f"❌ Failed to update settings: {e}")
        return False
    
    # Test 5: Show file content
    print("\n📄 Settings File Content:")
    print("-" * 30)
    try:
        with open(settings_file, 'r', encoding='utf-8') as f:
            content = f.read()
        print(content[:500] + "..." if len(content) > 500 else content)
    except Exception as e:
        print(f"Could not display file content: {e}")
    
    print("\n" + "=" * 40)
    print("✅ All settings save tests passed!")
    print(f"📁 Settings file location: {settings_file}")
    print(f"📊 File size: {settings_file.stat().st_size} bytes")
    
    return True

def test_simple_settings_panel():
    """Test SimpleSettingsPanel functionality."""
    print("\n🧪 Testing SimpleSettingsPanel")
    print("=" * 40)
    
    try:
        # Import the panel
        import sys
        from pathlib import Path
        sys.path.append(str(Path(__file__).parent.parent.parent))
        
        from streamtrade.gui.components.simple_settings_panel import SimpleSettingsPanel
        print("✓ SimpleSettingsPanel imported successfully")
        
        # Create panel instance
        panel = SimpleSettingsPanel()
        print("✓ SimpleSettingsPanel instance created")
        
        # Test settings loading
        current_settings = panel._load_current_settings()
        print("✓ Settings loaded successfully")
        
        # Verify default structure
        expected_keys = ['timezone', 'market_sessions', 'data_loading', 'cache', 'ui_preferences']
        for key in expected_keys:
            assert key in current_settings, f"Missing key: {key}"
        print("✓ Settings structure verified")
        
        # Test session calculation
        forex_sessions = panel._calculate_sessions('16:00')
        gold_sessions = panel._calculate_sessions('17:00')
        
        assert len(forex_sessions) == 6, "Should have 6 H4 sessions"
        assert len(gold_sessions) == 6, "Should have 6 H4 sessions"
        print("✓ Session calculation verified")
        
        print(f"  Forex sessions: {forex_sessions[0]}, {forex_sessions[1]}, ...")
        print(f"  Gold sessions: {gold_sessions[0]}, {gold_sessions[1]}, ...")
        
        # Test settings save
        test_data = {
            'timezone': {
                'data_timezone': 'UTC-6',
                'display_timezone': 'UTC+8'
            }
        }
        
        success = panel._save_settings(test_data)
        assert success, "Settings save should succeed"
        print("✓ Settings save functionality verified")
        
        return True
        
    except Exception as e:
        print(f"❌ SimpleSettingsPanel test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Phase 5.1 Settings Functionality Test")
    print("=" * 50)
    
    # Run tests
    test1_passed = test_settings_save()
    test2_passed = test_simple_settings_panel()
    
    print("\n" + "=" * 50)
    if test1_passed and test2_passed:
        print("✅ ALL TESTS PASSED!")
        print("\n📋 Settings System Status:")
        print("  ✓ JSON file save/load functionality")
        print("  ✓ Settings structure validation")
        print("  ✓ SimpleSettingsPanel component")
        print("  ✓ Session boundary calculation")
        print("  ✓ Settings persistence")
        print("\n🎯 Ready for frontend testing!")
        print("  1. Open browser to http://localhost:8502")
        print("  2. Click 'Phase 5.1 Settings' in sidebar")
        print("  3. Configure and save settings")
        print("  4. Check /streamtrade/config/user_settings.json")
    else:
        print("❌ SOME TESTS FAILED!")
        print("🔧 Please check the errors above")
