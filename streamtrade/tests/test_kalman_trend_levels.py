"""
Test cases for Lionaire - Kalman Trend indicator.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from streamtrade.indicators.custom.kalman_trend_levels import KalmanTrendLevels


class TestKalmanTrendLevels:
    """Test cases for Lionaire - Kalman Trend indicator."""
    
    @pytest.fixture
    def sample_data(self):
        """Create sample OHLCV data for testing."""
        dates = pd.date_range(start='2023-01-01', periods=200, freq='D')
        
        # Generate synthetic trending price data
        np.random.seed(42)
        base_price = 100
        trend = np.linspace(0, 15, 200)  # Upward trend
        noise = np.random.normal(0, 1.5, 200)
        
        close_prices = base_price + trend + noise
        
        # Generate OHLC from close prices
        high_prices = close_prices + np.random.uniform(0.5, 2, 200)
        low_prices = close_prices - np.random.uniform(0.5, 2, 200)
        open_prices = close_prices + np.random.uniform(-1, 1, 200)
        volume = np.random.randint(1000, 10000, 200)
        
        data = pd.DataFrame({
            'open': open_prices,
            'high': high_prices,
            'low': low_prices,
            'close': close_prices,
            'volume': volume
        }, index=dates)
        
        return data
    
    @pytest.fixture
    def indicator(self):
        """Create Lionaire - Kalman Trend indicator instance."""
        return KalmanTrendLevels()
    
    def test_indicator_initialization(self, indicator):
        """Test indicator initialization."""
        assert indicator.name == "KalmanTrendLevels"
        assert indicator.category == "trend"
        assert "Lionaire - Kalman Trend" in indicator.description
        
        # Check parameters
        param_names = [p.name for p in indicator.parameters]
        expected_params = ['short_length', 'long_length', 'upper_color', 'lower_color',
                          'fill_bullish_color', 'fill_bearish_color', 'fill_transparency', 'enable_fill']
        
        for param in expected_params:
            assert param in param_names
    
    def test_parameter_validation(self, indicator):
        """Test parameter validation."""
        # Valid parameters
        valid_params = {
            'short_length': 50,
            'long_length': 150,
            'upper_color': '#00ff00',
            'lower_color': '#ff0000'
        }
        
        validated = indicator.validate_parameters(**valid_params)
        assert validated['short_length'] == 50
        assert validated['long_length'] == 150
        assert validated['upper_color'] == '#00ff00'
        
        # Invalid parameters
        with pytest.raises(ValueError):
            indicator.validate_parameters(short_length=-10)
        
        with pytest.raises(ValueError):
            indicator.validate_parameters(long_length=1000)
    
    def test_kalman_filter(self, indicator, sample_data):
        """Test Kalman filter calculation."""
        close_prices = sample_data['close']
        
        # Test with default parameters
        filtered = indicator._kalman_filter(close_prices, length=50)
        
        assert len(filtered) == len(close_prices)
        assert not filtered.isna().all()  # Should have some valid values
        
        # Filtered values should be smoother than original
        original_volatility = close_prices.diff().std()
        filtered_volatility = filtered.diff().std()
        assert filtered_volatility < original_volatility
    
    def test_atr_calculation(self, indicator, sample_data):
        """Test ATR calculation."""
        atr = indicator._calculate_atr(sample_data)
        
        assert len(atr) == len(sample_data)
        # ATR should be positive where calculated
        valid_atr = atr.dropna()
        if len(valid_atr) > 0:
            assert (valid_atr >= 0).all()
    
    def test_calculate_basic(self, indicator, sample_data):
        """Test basic calculation."""
        results = indicator.calculate(sample_data)
        
        expected_keys = [
            'short_kalman', 'long_kalman', 'trend_up'
        ]
        
        for key in expected_keys:
            assert key in results.data
            assert len(results.data[key]) == len(sample_data)
    
    def test_calculate_custom_parameters(self, indicator, sample_data):
        """Test calculation with custom parameters."""
        parameters = {
            'short_length': 30,
            'long_length': 100,
            'upper_color': '#00ff00',
            'lower_color': '#ff0000'
        }
        
        results = indicator.calculate(sample_data, **parameters)
        
        # Should complete without errors
        assert 'short_kalman' in results.data
        assert 'long_kalman' in results.data
    
    def test_kalman_values_quality(self, indicator, sample_data):
        """Test quality of Kalman filter output."""
        results = indicator.calculate(sample_data)

        short_kalman = results.data['short_kalman']
        long_kalman = results.data['long_kalman']

        # Check that we have valid values
        assert not short_kalman.isna().all()
        assert not long_kalman.isna().all()

        # Check that long Kalman is smoother than short Kalman
        short_volatility = short_kalman.diff().std()
        long_volatility = long_kalman.diff().std()
        assert long_volatility <= short_volatility  # Long should be smoother
    
    def test_missing_data_columns(self, indicator):
        """Test handling of missing required columns."""
        # Data missing 'high' column
        incomplete_data = pd.DataFrame({
            'low': [1, 2, 3],
            'close': [1.5, 2.5, 3.5]
        })
        
        # Should return empty results due to missing columns
        results = indicator.calculate(incomplete_data)
        assert results.data == {}  # Empty results due to error
        assert not results.metadata.get('calculation_success', True)
    
    def test_empty_data(self, indicator):
        """Test handling of empty data."""
        empty_data = pd.DataFrame(columns=['open', 'high', 'low', 'close', 'volume'])
        
        # Should return empty results due to empty data
        results = indicator.calculate(empty_data)
        assert results.data == {}  # Empty results due to error
        assert not results.metadata.get('calculation_success', True)
    
    def test_nan_handling(self, indicator):
        """Test handling of NaN values in data."""
        # Create data with some NaN values
        dates = pd.date_range(start='2023-01-01', periods=50, freq='D')
        data = pd.DataFrame({
            'open': np.random.randn(50) + 100,
            'high': np.random.randn(50) + 102,
            'low': np.random.randn(50) + 98,
            'close': np.random.randn(50) + 100,
            'volume': np.random.randint(1000, 10000, 50)
        }, index=dates)
        
        # Introduce some NaN values
        data.loc[data.index[10:15], 'close'] = np.nan
        
        results = indicator.calculate(data)
        
        # Should handle NaN values gracefully
        assert 'short_kalman' in results.data
        assert len(results.data['short_kalman']) == len(data)


if __name__ == "__main__":
    # Run basic test
    indicator = KalmanTrendLevels()
    print(f"Indicator: {indicator.name}")
    print(f"Category: {indicator.category}")
    print(f"Description: {indicator.description}")
    
    # Test with sample data
    dates = pd.date_range(start='2023-01-01', periods=100, freq='D')
    np.random.seed(42)
    
    close_prices = 100 + np.cumsum(np.random.randn(100) * 0.5)
    high_prices = close_prices + np.random.uniform(0.5, 2, 100)
    low_prices = close_prices - np.random.uniform(0.5, 2, 100)
    open_prices = close_prices + np.random.uniform(-1, 1, 100)
    
    data = pd.DataFrame({
        'open': open_prices,
        'high': high_prices,
        'low': low_prices,
        'close': close_prices,
        'volume': np.random.randint(1000, 10000, 100)
    }, index=dates)
    
    try:
        results = indicator.calculate(data)
        print(f"\nCalculation successful!")
        print(f"Results keys: {list(results.data.keys())}")
        print(f"Data length: {len(results.data['short_kalman'])}")
        
        # Check trend analysis
        trend_up = results.data['trend_up']
        bullish_count = trend_up.sum()
        print(f"Bullish periods: {bullish_count}/{len(trend_up)}")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
