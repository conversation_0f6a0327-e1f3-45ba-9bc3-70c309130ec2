"""
Unit tests for DataManager and related components.
"""

import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from streamtrade.config.logging_config import setup_logging
from streamtrade.data.data_manager import DataManager
from streamtrade.data.data_loader import DataLoader
from streamtrade.data.timeframe_converter import TimeframeConverter
from streamtrade.config.settings import settings

# Setup logging for tests
setup_logging(level="WARNING", console_output=False)


class TestDataLoader(unittest.TestCase):
    """Test DataLoader functionality."""
    
    def setUp(self):
        self.data_loader = DataLoader()
    
    def test_discover_files(self):
        """Test file discovery."""
        # Test with a known pair
        if settings.available_pairs:
            pair = settings.available_pairs[0]
            files = self.data_loader.discover_files(pair)
            
            self.assertIsInstance(files, dict)
            # Should have at least some files if data exists
            if files:
                self.assertTrue(any(len(file_list) > 0 for file_list in files.values()))
    
    def test_get_available_date_range(self):
        """Test date range discovery."""
        if settings.available_pairs:
            pair = settings.available_pairs[0]
            date_range = self.data_loader.get_available_date_range(pair)
            
            if date_range:
                start_date, end_date = date_range
                self.assertIsInstance(start_date, datetime)
                self.assertIsInstance(end_date, datetime)
                self.assertLessEqual(start_date, end_date)


class TestTimeframeConverter(unittest.TestCase):
    """Test TimeframeConverter functionality."""
    
    def setUp(self):
        self.converter = TimeframeConverter()
        
        # Create sample M1 data for testing
        dates = pd.date_range(start='2024-01-01', periods=1440, freq='1min')  # 1 day of M1 data
        self.sample_data = pd.DataFrame({
            'open': np.random.uniform(1.0800, 1.0900, len(dates)),
            'high': np.random.uniform(1.0850, 1.0950, len(dates)),
            'low': np.random.uniform(1.0750, 1.0850, len(dates)),
            'close': np.random.uniform(1.0800, 1.0900, len(dates)),
            'volume': np.random.randint(100, 1000, len(dates))
        }, index=dates)
        
        # Fix OHLC relationships
        for i in range(len(self.sample_data)):
            row = self.sample_data.iloc[i]
            high = max(row['open'], row['close']) + np.random.uniform(0, 0.01)
            low = min(row['open'], row['close']) - np.random.uniform(0, 0.01)
            self.sample_data.iloc[i, self.sample_data.columns.get_loc('high')] = high
            self.sample_data.iloc[i, self.sample_data.columns.get_loc('low')] = low
    
    def test_validate_timeframe(self):
        """Test timeframe validation."""
        self.assertTrue(self.converter.validate_timeframe("H1"))
        self.assertTrue(self.converter.validate_timeframe("M5"))
        self.assertFalse(self.converter.validate_timeframe("INVALID"))
    
    def test_get_multiplier(self):
        """Test timeframe multipliers."""
        self.assertEqual(self.converter.get_multiplier("M1"), 1)
        self.assertEqual(self.converter.get_multiplier("M5"), 5)
        self.assertEqual(self.converter.get_multiplier("H1"), 60)
    
    def test_convert_timeframe_m5(self):
        """Test conversion to M5."""
        result = self.converter.convert_timeframe(self.sample_data, "M5")
        
        self.assertIsNotNone(result)
        self.assertIsInstance(result, pd.DataFrame)
        self.assertEqual(len(result), 288)  # 1440 / 5 = 288
        self.assertIn('open', result.columns)
        self.assertIn('high', result.columns)
        self.assertIn('low', result.columns)
        self.assertIn('close', result.columns)
        self.assertIn('volume', result.columns)
    
    def test_convert_timeframe_h1(self):
        """Test conversion to H1."""
        result = self.converter.convert_timeframe(self.sample_data, "H1")
        
        self.assertIsNotNone(result)
        self.assertIsInstance(result, pd.DataFrame)
        self.assertEqual(len(result), 24)  # 1440 / 60 = 24
    
    def test_convert_multiple_timeframes(self):
        """Test multiple timeframe conversion."""
        timeframes = ["M5", "M15", "H1"]
        results = self.converter.convert_multiple_timeframes(self.sample_data, timeframes)
        
        self.assertIsInstance(results, dict)
        self.assertEqual(len(results), 3)
        
        for tf in timeframes:
            self.assertIn(tf, results)
            self.assertIsInstance(results[tf], pd.DataFrame)


class TestDataManager(unittest.TestCase):
    """Test DataManager functionality."""
    
    def setUp(self):
        self.data_manager = DataManager()
    
    def test_get_available_pairs(self):
        """Test getting available pairs."""
        pairs = self.data_manager.get_available_pairs()
        self.assertIsInstance(pairs, list)
    
    def test_get_available_timeframes(self):
        """Test getting available timeframes."""
        timeframes = self.data_manager.get_available_timeframes()
        self.assertIsInstance(timeframes, list)
        self.assertIn("M1", timeframes)
        self.assertIn("H1", timeframes)
    
    def test_cache_functionality(self):
        """Test caching functionality."""
        # Clear cache first
        self.data_manager.clear_cache()
        
        # Get cache stats
        stats = self.data_manager.get_cache_stats()
        self.assertEqual(stats['entries'], 0)
    
    def test_memory_usage(self):
        """Test memory usage reporting."""
        mem_info = self.data_manager.get_memory_usage()
        self.assertIsInstance(mem_info, dict)
        self.assertIn('system_memory', mem_info)
        self.assertIn('cache_memory', mem_info)


class TestIntegration(unittest.TestCase):
    """Integration tests for the complete data pipeline."""
    
    def setUp(self):
        self.data_manager = DataManager()
    
    def test_data_loading_integration(self):
        """Test complete data loading pipeline."""
        # Skip if no data available
        pairs = self.data_manager.get_available_pairs()
        if not pairs:
            self.skipTest("No data available for testing")
        
        pair = pairs[0]
        
        # Test loading with different parameters
        data = self.data_manager.get_data(
            pair=pair,
            timeframe="H1",
            max_candles=10
        )
        
        if data is not None:
            self.assertIsInstance(data, pd.DataFrame)
            self.assertLessEqual(len(data), 10)
            self.assertTrue(all(col in data.columns for col in ['open', 'high', 'low', 'close', 'volume']))


def run_tests():
    """Run all tests."""
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestDataLoader,
        TestTimeframeConverter,
        TestDataManager,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
