"""
Test suite for strings and localization system.
"""

import unittest
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from streamtrade.config.strings import get_string, strings, get_project_info, get_app_config


class TestStrings(unittest.TestCase):
    """Test cases for strings and localization system."""
    
    def test_project_info(self):
        """Test project information retrieval."""
        project_info = get_project_info()
        
        self.assertIsInstance(project_info, dict)
        self.assertEqual(project_info["name"], "Lionaire")
        self.assertEqual(project_info["full_name"], "Lionaire Platform")
        self.assertIn("version", project_info)
        self.assertIn("author", project_info)
        self.assertIn("description", project_info)
    
    def test_app_config(self):
        """Test application configuration retrieval."""
        app_config = get_app_config()
        
        self.assertIsInstance(app_config, dict)
        self.assertEqual(app_config["page_title"], "Lionaire Platform")
        self.assertEqual(app_config["page_icon"], "📈")
        self.assertEqual(app_config["layout"], "wide")
    
    def test_get_string_basic(self):
        """Test basic string retrieval."""
        # Test APP category
        title = get_string("APP", "title")
        self.assertEqual(title, "📈 Lionaire Platform")
        
        subtitle = get_string("APP", "subtitle")
        self.assertEqual(subtitle, "Advanced Trading Analysis & Backtesting Platform")
        
        # Test DATA_SELECTION category
        data_title = get_string("DATA_SELECTION", "title")
        self.assertEqual(data_title, "📊 Data Selection")
        
        currency_pair = get_string("DATA_SELECTION", "currency_pair")
        self.assertEqual(currency_pair, "Currency Pair")
    
    def test_get_string_with_formatting(self):
        """Test string retrieval with formatting parameters."""
        # Test message with formatting
        error_msg = get_string("MESSAGES", "application_error", error="Test error")
        self.assertEqual(error_msg, "Application error: Test error")
        
        # Test switching timeframe message
        switching_msg = get_string("MESSAGES", "switching_timeframe", timeframe="H1")
        self.assertEqual(switching_msg, "Switching to H1...")
    
    def test_get_string_fallback(self):
        """Test string retrieval fallback for missing keys."""
        # Test non-existent category
        result = get_string("NONEXISTENT", "key")
        self.assertEqual(result, "key")
        
        # Test non-existent key in existing category
        result = get_string("APP", "nonexistent_key")
        self.assertEqual(result, "nonexistent_key")
    
    def test_strings_categories(self):
        """Test that all expected string categories exist."""
        expected_categories = [
            "PROJECT", "APP", "HEADER", "DATA_SELECTION", 
            "CHART", "CHART_STYLES", "CHART_THEMES", 
            "INDICATORS", "INDICATOR_CATEGORIES", 
            "MEMORY", "MESSAGES", "BUTTONS", "FILES"
        ]
        
        for category in expected_categories:
            self.assertTrue(hasattr(strings, category), f"Category {category} not found")
            category_dict = getattr(strings, category)
            self.assertIsInstance(category_dict, dict, f"Category {category} is not a dict")
    
    def test_project_strings(self):
        """Test PROJECT category strings."""
        project = strings.PROJECT
        
        required_keys = ["name", "full_name", "description", "version", "author"]
        for key in required_keys:
            self.assertIn(key, project, f"PROJECT.{key} not found")
            self.assertIsInstance(project[key], str, f"PROJECT.{key} is not a string")
            self.assertTrue(len(project[key]) > 0, f"PROJECT.{key} is empty")
    
    def test_app_strings(self):
        """Test APP category strings."""
        app = strings.APP
        
        required_keys = ["title", "subtitle", "footer"]
        for key in required_keys:
            self.assertIn(key, app, f"APP.{key} not found")
            self.assertIsInstance(app[key], str, f"APP.{key} is not a string")
            self.assertTrue(len(app[key]) > 0, f"APP.{key} is empty")
        
        # Test that title contains Lionaire
        self.assertIn("Lionaire", app["title"])
    
    def test_data_selection_strings(self):
        """Test DATA_SELECTION category strings."""
        data_sel = strings.DATA_SELECTION
        
        required_keys = [
            "title", "currency_pair", "currency_pair_help",
            "timeframe", "timeframe_help", "date_range"
        ]
        for key in required_keys:
            self.assertIn(key, data_sel, f"DATA_SELECTION.{key} not found")
            self.assertIsInstance(data_sel[key], str, f"DATA_SELECTION.{key} is not a string")
    
    def test_chart_strings(self):
        """Test CHART category strings."""
        chart = strings.CHART
        
        required_keys = [
            "title", "refresh_chart", "auto_scale", 
            "display_options", "show_volume", "show_grid"
        ]
        for key in required_keys:
            self.assertIn(key, chart, f"CHART.{key} not found")
            self.assertIsInstance(chart[key], str, f"CHART.{key} is not a string")
    
    def test_indicators_strings(self):
        """Test INDICATORS category strings."""
        indicators = strings.INDICATORS
        
        required_keys = [
            "title", "add_indicators", "manage_indicators",
            "summary", "total", "enabled", "disabled"
        ]
        for key in required_keys:
            self.assertIn(key, indicators, f"INDICATORS.{key} not found")
            self.assertIsInstance(indicators[key], str, f"INDICATORS.{key} is not a string")
    
    def test_messages_strings(self):
        """Test MESSAGES category strings."""
        messages = strings.MESSAGES
        
        required_keys = [
            "loading", "data_loaded", "application_error",
            "no_pairs_available", "please_load_data"
        ]
        for key in required_keys:
            self.assertIn(key, messages, f"MESSAGES.{key} not found")
            self.assertIsInstance(messages[key], str, f"MESSAGES.{key} is not a string")
    
    def test_chart_styles_and_themes(self):
        """Test chart styles and themes."""
        styles = strings.CHART_STYLES
        themes = strings.CHART_THEMES
        
        # Test styles
        self.assertIn("candlestick", styles)
        self.assertIn("ohlc_bars", styles)
        self.assertIn("line", styles)
        
        # Test themes
        self.assertIn("plotly", themes)
        self.assertIn("plotly_white", themes)
        self.assertIn("plotly_dark", themes)
    
    def test_indicator_categories(self):
        """Test indicator categories."""
        categories = strings.INDICATOR_CATEGORIES
        
        expected_categories = [
            "trend", "momentum", "volatility", 
            "volume", "support_resistance", "oscillators"
        ]
        
        for category in expected_categories:
            self.assertIn(category, categories, f"Indicator category {category} not found")
    
    def test_buttons_strings(self):
        """Test BUTTONS category strings."""
        buttons = strings.BUTTONS
        
        required_keys = [
            "load", "refresh", "export", "clear",
            "add", "remove", "save", "cancel"
        ]
        for key in required_keys:
            self.assertIn(key, buttons, f"BUTTONS.{key} not found")
            self.assertIsInstance(buttons[key], str, f"BUTTONS.{key} is not a string")


if __name__ == "__main__":
    unittest.main()
