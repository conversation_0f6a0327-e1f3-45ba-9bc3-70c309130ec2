"""
Test Phase 5.1 Implementation: Timezone-Aware Timeframe System
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import tempfile
import json
from pathlib import Path

# Add parent directory to path for imports
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

# Import the new components
from streamtrade.config.user_settings import UserSettings, user_settings
from streamtrade.data.session_aware_converter import SessionAwareConverter
from streamtrade.data.enhanced_data_manager import EnhancedDataManager


class TestUserSettings:
    """Test user settings system."""
    
    def test_default_settings_creation(self):
        """Test that default settings are created correctly."""
        # Create temporary settings for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_settings = UserSettings()
            temp_settings.config_dir = Path(temp_dir)
            temp_settings.settings_file = temp_settings.config_dir / 'test_settings.json'
            
            # Load settings (should create defaults)
            success = temp_settings.load_settings()
            
            # Should create defaults since file doesn't exist
            assert success == False  # False means created defaults
            assert temp_settings.settings_file.exists()
            
            # Check default values
            assert temp_settings.get('timezone.data_timezone') == 'UTC-5'
            assert temp_settings.get('timezone.display_timezone') == 'UTC+7'
            assert temp_settings.get('market_sessions.forex_open') == '16:00'
            assert temp_settings.get('market_sessions.non_forex_open') == '17:00'
            assert temp_settings.get('data_loading.days_back_default') == 5
    
    def test_settings_persistence(self):
        """Test that settings are saved and loaded correctly."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_settings = UserSettings()
            temp_settings.config_dir = Path(temp_dir)
            temp_settings.settings_file = temp_settings.config_dir / 'test_settings.json'
            
            # Set custom values
            temp_settings.set('timezone.data_timezone', 'UTC-8')
            temp_settings.set('data_loading.days_back_default', 10)
            
            # Save settings
            success = temp_settings.save_settings()
            assert success == True
            
            # Create new instance and load
            temp_settings2 = UserSettings()
            temp_settings2.config_dir = Path(temp_dir)
            temp_settings2.settings_file = temp_settings2.config_dir / 'test_settings.json'
            temp_settings2.load_settings()
            
            # Check values were persisted
            assert temp_settings2.get('timezone.data_timezone') == 'UTC-8'
            assert temp_settings2.get('data_loading.days_back_default') == 10
    
    def test_forex_pair_detection(self):
        """Test forex vs non-forex pair detection."""
        temp_settings = UserSettings()
        
        # Test forex pairs
        assert temp_settings.is_forex_pair('EURUSD') == True
        assert temp_settings.is_forex_pair('GBPJPY') == True
        
        # Test non-forex pairs
        assert temp_settings.is_forex_pair('XAUUSD') == False
        assert temp_settings.is_forex_pair('SPXUSD') == False
        assert temp_settings.is_forex_pair('NSXUSD') == False
    
    def test_market_open_time(self):
        """Test market open time retrieval."""
        temp_settings = UserSettings()
        
        # Forex pairs should use forex open time
        assert temp_settings.get_market_open_time('EURUSD') == '16:00'
        assert temp_settings.get_market_open_time('GBPJPY') == '16:00'
        
        # Non-forex pairs should use non-forex open time
        assert temp_settings.get_market_open_time('XAUUSD') == '17:00'
        assert temp_settings.get_market_open_time('SPXUSD') == '17:00'


class TestSessionAwareConverter:
    """Test session-aware timeframe converter."""
    
    def setup_method(self):
        """Setup test data."""
        self.converter = SessionAwareConverter()
        
        # Create sample M1 data for testing
        start_time = datetime(2024, 1, 15, 16, 0)  # Monday 16:00
        times = [start_time + timedelta(minutes=i) for i in range(240)]  # 4 hours of M1 data
        
        self.sample_m1_data = pd.DataFrame({
            'open': np.random.uniform(1.0800, 1.0900, len(times)),
            'high': np.random.uniform(1.0850, 1.0950, len(times)),
            'low': np.random.uniform(1.0750, 1.0850, len(times)),
            'close': np.random.uniform(1.0800, 1.0900, len(times)),
            'volume': np.random.randint(100, 1000, len(times))
        }, index=pd.DatetimeIndex(times))
        
        # Ensure OHLC logic is correct
        for i in range(len(self.sample_m1_data)):
            row = self.sample_m1_data.iloc[i]
            high = max(row['open'], row['close']) + np.random.uniform(0, 0.01)
            low = min(row['open'], row['close']) - np.random.uniform(0, 0.01)
            self.sample_m1_data.iloc[i, self.sample_m1_data.columns.get_loc('high')] = high
            self.sample_m1_data.iloc[i, self.sample_m1_data.columns.get_loc('low')] = low
    
    def test_session_boundaries_forex(self):
        """Test session boundary calculation for forex pairs."""
        date = datetime(2024, 1, 15)  # Monday
        session_info = self.converter.get_session_boundaries('EURUSD', date)
        
        assert session_info['is_forex'] == True
        assert session_info['market_open'] == '16:00'
        assert len(session_info['h4_sessions']) == 6
        
        # Check first H4 session
        first_session = session_info['h4_sessions'][0]
        assert first_session['start'].hour == 16
        assert first_session['start'].minute == 0
        assert first_session['end'].hour == 20
    
    def test_session_boundaries_non_forex(self):
        """Test session boundary calculation for non-forex pairs."""
        date = datetime(2024, 1, 15)  # Monday
        session_info = self.converter.get_session_boundaries('XAUUSD', date)
        
        assert session_info['is_forex'] == False
        assert session_info['market_open'] == '17:00'
        assert len(session_info['h4_sessions']) == 6
        
        # Check first H4 session
        first_session = session_info['h4_sessions'][0]
        assert first_session['start'].hour == 17
        assert first_session['start'].minute == 0
        assert first_session['end'].hour == 21
    
    def test_h4_conversion(self):
        """Test M1 to H4 conversion."""
        h4_data = self.converter.convert_to_h4(self.sample_m1_data, 'EURUSD')
        
        assert not h4_data.empty
        assert len(h4_data) == 1  # 4 hours of M1 data should create 1 H4 candle
        
        # Check OHLC logic
        h4_candle = h4_data.iloc[0]
        m1_data_subset = self.sample_m1_data
        
        assert h4_candle['open'] == m1_data_subset['open'].iloc[0]
        assert h4_candle['close'] == m1_data_subset['close'].iloc[-1]
        assert h4_candle['high'] == m1_data_subset['high'].max()
        assert h4_candle['low'] == m1_data_subset['low'].min()
    
    def test_timeframe_conversion_caching(self):
        """Test that conversion results are cached."""
        # First conversion
        result1 = self.converter.convert_timeframe(self.sample_m1_data, 'H4', 'EURUSD')
        
        # Second conversion (should use cache)
        result2 = self.converter.convert_timeframe(self.sample_m1_data, 'H4', 'EURUSD')
        
        # Results should be identical
        pd.testing.assert_frame_equal(result1, result2)
        
        # Check cache info
        cache_info = self.converter.get_cache_info()
        assert cache_info['cache_size'] > 0


class TestEnhancedDataManager:
    """Test enhanced data manager."""
    
    def setup_method(self):
        """Setup test environment."""
        # Note: This test requires actual data files to work properly
        # For now, we'll test the interface and basic functionality
        pass
    
    def test_initialization(self):
        """Test that enhanced data manager initializes correctly."""
        try:
            manager = EnhancedDataManager()
            assert manager is not None
            assert hasattr(manager, 'session_converter')
            assert hasattr(manager, 'user_settings')
            assert hasattr(manager, 'max_candles_load')
            assert hasattr(manager, 'max_candles_display')
        except Exception as e:
            # If initialization fails due to missing data, that's expected in test environment
            pytest.skip(f"Enhanced data manager requires data files: {e}")
    
    def test_user_settings_integration(self):
        """Test integration with user settings."""
        try:
            manager = EnhancedDataManager()
            
            # Test that settings are loaded
            assert manager.max_candles_load > 0
            assert manager.max_candles_display > 0
            
            # Test settings update
            manager.update_user_settings()
            
        except Exception as e:
            pytest.skip(f"Enhanced data manager requires data files: {e}")
    
    def test_cache_info(self):
        """Test cache information retrieval."""
        try:
            manager = EnhancedDataManager()
            cache_info = manager.get_cache_info()
            
            # Check that cache info has expected keys
            expected_keys = [
                'data_cache_entries', 'data_cache_size_mb',
                'm1_cache_entries', 'm1_cache_size_mb',
                'total_cache_size_mb', 'max_cache_size_mb',
                'cache_utilization_percent', 'user_contexts'
            ]
            
            for key in expected_keys:
                assert key in cache_info
                
        except Exception as e:
            pytest.skip(f"Enhanced data manager requires data files: {e}")


def test_global_user_settings():
    """Test that global user settings instance works."""
    # Test that global instance is available
    assert user_settings is not None
    
    # Test basic functionality
    timezone_settings = user_settings.get_timezone_settings()
    assert 'data_timezone' in timezone_settings
    assert 'display_timezone' in timezone_settings
    
    market_settings = user_settings.get_market_session_settings()
    assert 'forex_open' in market_settings
    assert 'non_forex_open' in market_settings


def test_integration_compatibility():
    """Test that new components are compatible with existing system."""
    # Test imports
    try:
        from streamtrade.data import enhanced_data_manager, data_manager
        from streamtrade.config.user_settings import user_settings
        
        # Both should be available
        assert data_manager is not None  # Backward compatibility
        # enhanced_data_manager might be None if initialization failed
        
        assert user_settings is not None
        
    except ImportError as e:
        pytest.fail(f"Import failed: {e}")


if __name__ == "__main__":
    # Run basic tests
    print("Testing Phase 5.1 Implementation...")
    
    # Test user settings
    print("✓ Testing UserSettings...")
    test_user_settings = TestUserSettings()
    test_user_settings.test_default_settings_creation()
    test_user_settings.test_forex_pair_detection()
    test_user_settings.test_market_open_time()
    
    # Test session converter
    print("✓ Testing SessionAwareConverter...")
    test_converter = TestSessionAwareConverter()
    test_converter.setup_method()
    test_converter.test_session_boundaries_forex()
    test_converter.test_session_boundaries_non_forex()
    test_converter.test_h4_conversion()
    
    # Test global instances
    print("✓ Testing global instances...")
    test_global_user_settings()
    test_integration_compatibility()
    
    print("✅ All Phase 5.1 tests passed!")
