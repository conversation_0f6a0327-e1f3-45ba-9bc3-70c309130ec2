# Debug Test Scripts

This directory contains debug and testing scripts for the Lionaire platform.

## 🔍 Debug Scripts

### Core Testing Scripts
- **`debug_ohlc_conversion.py`** - Debug script for OHLC conversion issues
- **`test_all_timeframes.py`** - Comprehensive test for all timeframe conversions
- **`test_h4_conversion.py`** - Specific test for H4 timeframe conversion
- **`test_debug_logging.py`** - Test debug logging system functionality

### Data Testing Scripts
- **`test_date_range.py`** - Test available date range for currency pairs

### Test Runners
- **`run_debug_test.py`** - Script runner for debug tests with proper environment setup

## 🚀 How to Run

### Prerequisites
```bash
# Activate virtual environment
source /home/<USER>/Learn/python/notebooks/venv/bin/activate

# Navigate to streamtrade directory
cd /home/<USER>/Learn/python/notebooks/streamtrade
```

### Running Individual Tests
```bash
# Test all timeframes
python tests/debug/test_all_timeframes.py

# Test H4 conversion specifically
python tests/debug/test_h4_conversion.py

# Test debug logging system
python tests/debug/test_debug_logging.py

# Test available date ranges
python tests/debug/test_date_range.py

# Debug OHLC conversion issues
python tests/debug/debug_ohlc_conversion.py
```

### Using Test Runner
```bash
# Run debug test with proper environment
python tests/debug/run_debug_test.py
```

## 📊 Test Coverage

### Timeframe Testing
- **M5, M15, M30, H1, H4, D1** - All major timeframes
- **OHLC Validation** - Ensures proper Open/High/Low/Close relationships
- **Interval Validation** - Verifies correct time intervals between candles
- **Conversion Accuracy** - Tests M1 to higher timeframe conversion

### Data Testing
- **Date Range Validation** - Tests available data ranges for pairs
- **Cache Behavior** - Tests cache hits vs fresh loads
- **Multi-Pair Support** - Tests independent pair processing

### Debug System Testing
- **Real-time Monitoring** - Tests debug output during operations
- **Cache Detection** - Tests cache hit/miss identification
- **Performance Tracking** - Tests loading time measurements

## 🎯 Expected Results

### Successful Test Output
```
🔍 TESTING ALL TIMEFRAME CONVERSIONS
======================================================================
📊 Pair: GBPUSD
📅 Days Back: 7

1️⃣ Testing M5 conversion...
✅ M5: 2016 candles loaded
   📅 Range: 2024-12-24 16:05:00 to 2024-12-31 15:55:00
   📊 OHLC Valid: ✅ (0 invalid)
   ⏰ Interval Valid: ✅

[... similar output for other timeframes ...]

🎯 Overall Success Rate: 6/6 (100.0%)
🎉 ALL TIMEFRAMES WORKING PERFECTLY!
```

### Debug Logging Output
```
============================================================
🔄 INITIAL DATA LOADING
============================================================
📊 Pair: EURUSD
⏰ Timeframe: H1
📅 Days Back: 3
📅 Date Range: 2024-12-28 to 2024-12-31

📁 Data Source: FRESH LOAD FROM FILES
📊 M1 Candles Loaded: 1,858
📊 Converted Candles: 32
📺 Displayed Candles: 32
============================================================
```

## ⚠️ Troubleshooting

### Common Issues
1. **Import Errors**: Ensure you're running from the streamtrade root directory
2. **Data Not Found**: Check that histdata files are available in the expected location
3. **Permission Errors**: Ensure cache directory is writable

### Debug Tips
- Check terminal output for detailed error messages
- Verify virtual environment is activated
- Ensure all dependencies are installed
- Check log files in `logs/` directory for additional information

## 📝 Notes

These debug scripts were created during the development process to identify and fix critical issues:
- OHLC conversion problems (resolved in Phase 6)
- Cache behavior validation (implemented in Phase 5.4)
- Debug logging system (implemented in Phase 5.4+)
- Timeframe switching optimization (implemented in Phase 4)

All scripts are maintained for regression testing and future debugging needs.
