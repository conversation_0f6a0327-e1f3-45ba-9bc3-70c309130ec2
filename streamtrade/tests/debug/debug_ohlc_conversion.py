#!/usr/bin/env python3
"""
Debug script untuk menganalisis OHLC conversion issue.
"""

import sys
from pathlib import Path
import pandas as pd
from datetime import datetime, timedelta

# Add current directory to path for proper imports
sys.path.append(str(Path(__file__).parent))

from data.enhanced_data_manager import EnhancedDataManager
from config.logging_config import setup_logging

def debug_ohlc_conversion():
    """Debug OHLC conversion untuk mengidentifikasi masalah."""
    
    # Setup logging
    setup_logging(level="INFO", console_output=True)
    
    # Initialize Enhanced Data Manager
    data_manager = EnhancedDataManager()
    
    pair = "GBPUSD"
    days_back = 3
    
    print(f"🔍 DEBUGGING OHLC CONVERSION")
    print(f"{'='*60}")
    print(f"📊 Pair: {pair}")
    print(f"📅 Days Back: {days_back}")
    
    # Load M15 data
    print(f"\n1️⃣ Loading M15 data...")
    m15_data = data_manager.load_n_days_back(pair, "M15", days_back)
    
    if m15_data is None or m15_data.empty:
        print("❌ Failed to load M15 data")
        return
    
    print(f"✅ M15 data loaded: {len(m15_data)} candles")
    print(f"📅 M15 range: {m15_data.index[0]} to {m15_data.index[-1]}")
    
    # Load H4 data
    print(f"\n2️⃣ Loading H4 data...")
    h4_data = data_manager.load_n_days_back(pair, "H4", days_back)
    
    if h4_data is None or h4_data.empty:
        print("❌ Failed to load H4 data")
        return
    
    print(f"✅ H4 data loaded: {len(h4_data)} candles")
    print(f"📅 H4 range: {h4_data.index[0]} to {h4_data.index[-1]}")
    
    # Analyze M15 vs H4 data
    print(f"\n3️⃣ Analyzing M15 vs H4 data...")
    
    # Check price ranges
    m15_high = m15_data['high'].max()
    m15_low = m15_data['low'].min()
    h4_high = h4_data['high'].max()
    h4_low = h4_data['low'].min()
    
    print(f"📊 M15 Price Range: {m15_low:.5f} - {m15_high:.5f}")
    print(f"📊 H4 Price Range: {h4_low:.5f} - {h4_high:.5f}")
    
    # Check for anomalies
    if abs(h4_high - m15_high) > 0.01 or abs(h4_low - m15_low) > 0.01:
        print("⚠️  WARNING: Significant price range difference detected!")
    
    # Analyze individual H4 candles
    print(f"\n4️⃣ Analyzing individual H4 candles...")
    
    for i, (timestamp, candle) in enumerate(h4_data.iterrows()):
        if i >= 5:  # Limit to first 5 candles
            break
            
        print(f"\n📊 H4 Candle {i+1}: {timestamp}")
        print(f"   O: {candle['open']:.5f}")
        print(f"   H: {candle['high']:.5f}")
        print(f"   L: {candle['low']:.5f}")
        print(f"   C: {candle['close']:.5f}")
        
        # Check for anomalies
        if candle['high'] < candle['open'] or candle['high'] < candle['close']:
            print("   ❌ ERROR: High is lower than Open/Close!")
        if candle['low'] > candle['open'] or candle['low'] > candle['close']:
            print("   ❌ ERROR: Low is higher than Open/Close!")
        if candle['high'] < candle['low']:
            print("   ❌ ERROR: High is lower than Low!")
        
        # Calculate candle size
        candle_size = candle['high'] - candle['low']
        print(f"   📏 Candle Size: {candle_size:.5f}")
        
        if candle_size > 0.01:  # Large candle (>100 pips for major pairs)
            print("   ⚠️  WARNING: Very large candle detected!")
    
    # Compare with M15 data in same time range
    print(f"\n5️⃣ Comparing with M15 data in same time range...")
    
    if len(h4_data) > 0:
        first_h4_time = h4_data.index[0]
        last_h4_time = h4_data.index[-1]
        
        # Filter M15 data for same time range
        m15_filtered = m15_data[(m15_data.index >= first_h4_time) & 
                               (m15_data.index <= last_h4_time + timedelta(hours=4))]
        
        if not m15_filtered.empty:
            print(f"📊 M15 candles in H4 range: {len(m15_filtered)}")
            print(f"📊 Expected M15 candles: {len(h4_data) * 16}")  # 16 M15 per H4
            
            # Check price continuity
            m15_price_range = m15_filtered['high'].max() - m15_filtered['low'].min()
            h4_price_range = h4_data['high'].max() - h4_data['low'].min()
            
            print(f"📊 M15 total price range: {m15_price_range:.5f}")
            print(f"📊 H4 total price range: {h4_price_range:.5f}")
            
            if abs(m15_price_range - h4_price_range) > 0.001:
                print("⚠️  WARNING: Price range mismatch between M15 and H4!")
    
    print(f"\n6️⃣ Recommendations...")
    
    # Check for potential solutions
    if len(h4_data) < (days_back * 6):  # Expected ~6 H4 per day
        print("💡 Consider: Session boundary adjustment")
    
    print("💡 Consider: Using standard resampling instead of session-based")
    print("💡 Consider: Gap detection and handling improvement")
    print("💡 Consider: Data validation before conversion")
    
    print(f"\n{'='*60}")
    print("🎯 Debug analysis completed!")

if __name__ == "__main__":
    debug_ohlc_conversion()
