#!/usr/bin/env python3
"""
Test script untuk semua timeframe conversion setelah perbaikan.
"""

import sys
from pathlib import Path
import pandas as pd

# Add parent directory to path for proper imports
sys.path.append(str(Path(__file__).parent.parent))

from streamtrade.data.enhanced_data_manager import EnhancedDataManager
from streamtrade.config.logging_config import setup_logging

def test_all_timeframes():
    """Test semua timeframe conversion."""
    
    # Setup logging
    setup_logging(level="INFO", console_output=True)
    
    # Initialize Enhanced Data Manager
    data_manager = EnhancedDataManager()
    
    pair = "GBPUSD"
    days_back = 7  # 1 week for comprehensive test
    
    print(f"🔍 TESTING ALL TIMEFRAME CONVERSIONS")
    print(f"{'='*70}")
    print(f"📊 Pair: {pair}")
    print(f"📅 Days Back: {days_back}")
    
    # Test timeframes in order of complexity
    timeframes_to_test = ['M5', 'M15', 'M30', 'H1', 'H4', 'D1']
    
    results = {}
    
    for i, tf in enumerate(timeframes_to_test):
        print(f"\n{i+1}️⃣ Testing {tf} conversion...")
        
        try:
            data = data_manager.load_n_days_back(pair, tf, days_back)
            
            if data is None or data.empty:
                print(f"❌ {tf}: Failed to load data")
                results[tf] = {'status': 'FAILED', 'candles': 0, 'error': 'No data'}
                continue
            
            # Basic validation
            candle_count = len(data)
            date_range = f"{data.index[0]} to {data.index[-1]}"
            
            # Check OHLC validity
            ohlc_valid = True
            invalid_candles = 0
            
            for idx, candle in data.iterrows():
                if (candle['high'] < candle['open'] or 
                    candle['high'] < candle['close'] or
                    candle['low'] > candle['open'] or 
                    candle['low'] > candle['close'] or
                    candle['high'] < candle['low']):
                    ohlc_valid = False
                    invalid_candles += 1
            
            # Check time intervals (for first few candles)
            interval_valid = True
            expected_intervals = {
                'M5': pd.Timedelta(minutes=5),
                'M15': pd.Timedelta(minutes=15),
                'M30': pd.Timedelta(minutes=30),
                'H1': pd.Timedelta(hours=1),
                'H4': pd.Timedelta(hours=4),
                'D1': pd.Timedelta(days=1)
            }
            
            if len(data) > 1 and tf in expected_intervals:
                actual_interval = data.index[1] - data.index[0]
                expected_interval = expected_intervals[tf]
                if actual_interval != expected_interval:
                    interval_valid = False
            
            # Store results
            results[tf] = {
                'status': 'SUCCESS',
                'candles': candle_count,
                'date_range': date_range,
                'ohlc_valid': ohlc_valid,
                'invalid_candles': invalid_candles,
                'interval_valid': interval_valid,
                'actual_interval': actual_interval if len(data) > 1 else None,
                'expected_interval': expected_intervals.get(tf)
            }
            
            print(f"✅ {tf}: {candle_count} candles loaded")
            print(f"   📅 Range: {date_range}")
            print(f"   📊 OHLC Valid: {'✅' if ohlc_valid else '❌'} ({invalid_candles} invalid)")
            print(f"   ⏰ Interval Valid: {'✅' if interval_valid else '❌'}")
            
            if not interval_valid:
                print(f"      Expected: {expected_intervals.get(tf)}, Got: {actual_interval}")
            
        except Exception as e:
            print(f"❌ {tf}: Exception - {str(e)}")
            results[tf] = {'status': 'ERROR', 'candles': 0, 'error': str(e)}
    
    # Summary report
    print(f"\n{'='*70}")
    print(f"📊 SUMMARY REPORT")
    print(f"{'='*70}")
    
    success_count = 0
    total_count = len(timeframes_to_test)
    
    for tf in timeframes_to_test:
        result = results.get(tf, {})
        status = result.get('status', 'UNKNOWN')
        candles = result.get('candles', 0)
        
        if status == 'SUCCESS':
            success_count += 1
            ohlc_status = '✅' if result.get('ohlc_valid', False) else '❌'
            interval_status = '✅' if result.get('interval_valid', False) else '❌'
            print(f"✅ {tf:4s}: {candles:4d} candles | OHLC: {ohlc_status} | Interval: {interval_status}")
        else:
            error = result.get('error', 'Unknown error')
            print(f"❌ {tf:4s}: FAILED - {error}")
    
    print(f"\n🎯 Overall Success Rate: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    
    if success_count == total_count:
        print("🎉 ALL TIMEFRAMES WORKING PERFECTLY!")
    else:
        print(f"⚠️  {total_count - success_count} timeframes need attention")
    
    # Test timeframe switching
    print(f"\n{'='*70}")
    print(f"🔄 TESTING TIMEFRAME SWITCHING")
    print(f"{'='*70}")
    
    # Start with M15, then switch to others
    print(f"Loading base M15 data...")
    base_data = data_manager.load_n_days_back(pair, "M15", days_back)
    
    if base_data is not None:
        print(f"✅ Base M15 loaded: {len(base_data)} candles")
        
        switch_timeframes = ['H1', 'H4', 'D1', 'M5']
        
        for tf in switch_timeframes:
            print(f"Switching to {tf}...")
            switched_data = data_manager.switch_timeframe(pair, tf)
            
            if switched_data is not None and not switched_data.empty:
                print(f"✅ {tf} switch: {len(switched_data)} candles")
            else:
                print(f"❌ {tf} switch: Failed")
    else:
        print("❌ Cannot test switching - base data failed")
    
    print(f"\n{'='*70}")
    print("🎯 All Timeframe Test Completed!")

if __name__ == "__main__":
    test_all_timeframes()
