"""
Unit tests for visualization module.
"""

import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from streamtrade.config.logging_config import setup_logging
from streamtrade.visualization.chart_viewer import <PERSON><PERSON>iewer
from streamtrade.visualization.plotly_charts import PlotlyCharts
from streamtrade.indicators.base_indicator import IndicatorResult

# Setup logging for tests
setup_logging(level="WARNING", console_output=False)


class TestPlotlyCharts(unittest.TestCase):
    """Test PlotlyCharts functionality."""
    
    def setUp(self):
        self.plotly_charts = PlotlyCharts()
        
        # Create sample OHLCV data
        dates = pd.date_range(start='2024-01-01', periods=50, freq='1h')
        np.random.seed(42)
        
        self.sample_data = pd.DataFrame({
            'open': np.random.uniform(1.0800, 1.0900, len(dates)),
            'high': np.random.uniform(1.0850, 1.0950, len(dates)),
            'low': np.random.uniform(1.0750, 1.0850, len(dates)),
            'close': np.random.uniform(1.0800, 1.0900, len(dates)),
            'volume': np.random.randint(100, 1000, len(dates))
        }, index=dates)
        
        # Fix OHLC relationships
        for i in range(len(self.sample_data)):
            row = self.sample_data.iloc[i]
            high = max(row['open'], row['close']) + 0.001
            low = min(row['open'], row['close']) - 0.001
            self.sample_data.iloc[i, self.sample_data.columns.get_loc('high')] = high
            self.sample_data.iloc[i, self.sample_data.columns.get_loc('low')] = low
    
    def test_create_candlestick_chart(self):
        """Test creating basic candlestick chart."""
        fig = self.plotly_charts.create_candlestick_chart(
            data=self.sample_data,
            title="Test Chart"
        )
        
        self.assertIsNotNone(fig)
        self.assertEqual(fig.layout.title.text, "Test Chart")
        self.assertTrue(len(fig.data) > 0)
    
    def test_create_chart_with_indicators(self):
        """Test creating chart with indicators."""
        # Create mock indicator result
        sma_data = self.sample_data['close'].rolling(window=10).mean()
        
        indicator_result = IndicatorResult(
            name="SMA",
            data={'sma': sma_data},
            parameters={'period': 10},
            metadata={'category': 'trend', 'calculation_success': True}
        )
        
        indicators = {'sma_10': indicator_result}
        
        fig = self.plotly_charts.create_candlestick_chart(
            data=self.sample_data,
            indicators=indicators,
            title="Chart with Indicators"
        )
        
        self.assertIsNotNone(fig)
        # Should have candlestick + indicator traces
        self.assertTrue(len(fig.data) >= 2)
    
    def test_create_simple_line_chart(self):
        """Test creating simple line chart."""
        series_data = self.sample_data['close']
        
        fig = self.plotly_charts.create_simple_line_chart(
            data=series_data,
            title="Line Chart Test",
            color="blue"
        )
        
        self.assertIsNotNone(fig)
        self.assertEqual(fig.layout.title.text, "Line Chart Test")
        self.assertEqual(len(fig.data), 1)
    
    def test_error_handling(self):
        """Test error handling in chart creation."""
        # Test with empty data
        empty_data = pd.DataFrame()
        
        fig = self.plotly_charts.create_candlestick_chart(
            data=empty_data,
            title="Error Test"
        )
        
        # Should return error chart instead of crashing
        self.assertIsNotNone(fig)


class TestChartViewer(unittest.TestCase):
    """Test ChartViewer functionality."""
    
    def setUp(self):
        self.chart_viewer = ChartViewer()
    
    def test_initialization(self):
        """Test ChartViewer initialization."""
        self.assertIsNotNone(self.chart_viewer.indicator_manager)
        self.assertIsNotNone(self.chart_viewer.plotly_charts)
        self.assertIsNone(self.chart_viewer.current_data)
        self.assertIsNone(self.chart_viewer.current_pair)
        self.assertIsNone(self.chart_viewer.current_timeframe)
    
    def test_get_available_data(self):
        """Test getting available pairs and timeframes."""
        pairs = self.chart_viewer.get_available_pairs()
        timeframes = self.chart_viewer.get_available_timeframes()
        
        self.assertIsInstance(pairs, list)
        self.assertIsInstance(timeframes, list)
        
        # Should have some basic timeframes
        expected_timeframes = ['M1', 'M5', 'H1', 'D1']
        for tf in expected_timeframes:
            self.assertIn(tf, timeframes)
    
    def test_indicator_management(self):
        """Test indicator management through ChartViewer."""
        # Test adding indicator
        success = self.chart_viewer.add_indicator(
            name="test_sma",
            indicator_type="SMA",
            parameters={"period": 20}
        )
        self.assertTrue(success)
        
        # Test getting indicator info
        info = self.chart_viewer.get_indicator_info("test_sma")
        self.assertIsNotNone(info)
        self.assertEqual(info['name'], 'SMA')
        
        # Test removing indicator
        success = self.chart_viewer.remove_indicator("test_sma")
        self.assertTrue(success)
        
        info = self.chart_viewer.get_indicator_info("test_sma")
        self.assertIsNone(info)
    
    def test_get_available_indicators(self):
        """Test getting available indicators."""
        indicators = self.chart_viewer.get_available_indicators()
        self.assertIsInstance(indicators, dict)
        
        # Should have basic indicators
        expected_indicators = ['SMA', 'EMA', 'RSI', 'MACD']
        for indicator in expected_indicators:
            self.assertIn(indicator, indicators)
        
        # Test indicators by category
        by_category = self.chart_viewer.get_indicators_by_category()
        self.assertIsInstance(by_category, dict)
        self.assertIn('trend', by_category)
        self.assertIn('momentum', by_category)
    
    def test_chart_info(self):
        """Test getting chart information."""
        info = self.chart_viewer.get_chart_info()
        
        self.assertIsInstance(info, dict)
        self.assertIn('pair', info)
        self.assertIn('timeframe', info)
        self.assertIn('data_points', info)
        self.assertIn('date_range', info)
        self.assertIn('indicators', info)
        self.assertIn('indicator_count', info)
        
        # Should show no data initially
        self.assertIsNone(info['pair'])
        self.assertEqual(info['data_points'], 0)
    
    def test_create_empty_chart(self):
        """Test creating chart with no data."""
        fig = self.chart_viewer.create_chart()
        
        # Should create empty chart placeholder
        self.assertIsNotNone(fig)
    
    def test_export_import_config(self):
        """Test exporting and importing indicator configuration."""
        # Add some indicators
        self.chart_viewer.add_indicator("sma_20", "SMA", {"period": 20})
        self.chart_viewer.add_indicator("rsi_14", "RSI", {"period": 14})
        
        # Export configuration
        config_json = self.chart_viewer.export_indicator_config()
        self.assertIsInstance(config_json, str)
        
        # Clear indicators
        self.chart_viewer.indicator_manager.clear_all()
        
        # Import configuration
        success = self.chart_viewer.import_indicator_config(config_json)
        self.assertTrue(success)
        
        # Check indicators were restored
        info = self.chart_viewer.indicator_manager.get_all_indicators_info()
        self.assertEqual(len(info), 2)
        self.assertIn("sma_20", info)
        self.assertIn("rsi_14", info)


class TestIntegration(unittest.TestCase):
    """Integration tests for visualization components."""
    
    def setUp(self):
        self.chart_viewer = ChartViewer()
        
        # Create sample data for testing
        dates = pd.date_range(start='2024-01-01', periods=100, freq='1h')
        self.sample_data = pd.DataFrame({
            'open': np.random.uniform(1.0800, 1.0900, len(dates)),
            'high': np.random.uniform(1.0850, 1.0950, len(dates)),
            'low': np.random.uniform(1.0750, 1.0850, len(dates)),
            'close': np.random.uniform(1.0800, 1.0900, len(dates)),
            'volume': np.random.randint(100, 1000, len(dates))
        }, index=dates)
    
    def test_full_workflow(self):
        """Test complete workflow from data to chart."""
        # Simulate loading data (we can't test actual data loading without files)
        self.chart_viewer.current_data = self.sample_data
        self.chart_viewer.current_pair = "EURUSD"
        self.chart_viewer.current_timeframe = "H1"
        
        # Add indicators
        self.chart_viewer.add_indicator("sma_20", "SMA", {"period": 20})
        self.chart_viewer.add_indicator("rsi_14", "RSI", {"period": 14})
        
        # Create chart
        fig = self.chart_viewer.create_chart()
        
        self.assertIsNotNone(fig)
        
        # Check chart info
        info = self.chart_viewer.get_chart_info()
        self.assertEqual(info['pair'], "EURUSD")
        self.assertEqual(info['timeframe'], "H1")
        self.assertEqual(info['data_points'], len(self.sample_data))
        self.assertEqual(info['indicator_count'], 2)
    
    def test_indicator_calculation_integration(self):
        """Test indicator calculation with chart creation."""
        # Set sample data
        self.chart_viewer.current_data = self.sample_data
        
        # Add multiple indicators
        self.chart_viewer.add_indicator("sma_10", "SMA", {"period": 10})
        self.chart_viewer.add_indicator("sma_20", "SMA", {"period": 20})
        self.chart_viewer.add_indicator("rsi_14", "RSI", {"period": 14})
        self.chart_viewer.add_indicator("macd", "MACD", {"fast": 12, "slow": 26, "signal": 9})
        
        # Calculate indicators
        self.chart_viewer._calculate_indicators()
        
        # Check results
        self.assertEqual(len(self.chart_viewer.current_indicators), 4)
        
        # Create chart with indicators
        fig = self.chart_viewer.create_chart()
        self.assertIsNotNone(fig)
        
        # Should have multiple traces (candlestick + indicators)
        self.assertTrue(len(fig.data) > 4)


def run_tests():
    """Run all visualization tests."""
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestPlotlyCharts,
        TestChartViewer,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
