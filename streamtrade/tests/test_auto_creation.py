"""
Test auto-creation of settings file when deleted.
"""

import os
import json
from pathlib import Path

def test_auto_creation_scenarios():
    """Test various auto-creation scenarios."""
    print("🧪 Testing Auto-Creation Scenarios")
    print("=" * 50)
    
    project_dir = Path(__file__).parent.parent
    settings_file = project_dir / 'config' / 'user_settings.json'
    
    # Scenario 1: File doesn't exist, load defaults
    print("\n📋 Scenario 1: File doesn't exist, load defaults")
    
    # Ensure file is deleted
    if settings_file.exists():
        settings_file.unlink()
        print("✓ Existing file deleted")
    
    # Import and create panel
    import sys
    sys.path.append(str(project_dir.parent))
    from streamtrade.gui.components.simple_settings_panel import SimpleSettingsPanel
    
    panel = SimpleSettingsPanel()
    current_settings = panel._load_current_settings()
    
    print(f"✓ Settings loaded: {len(current_settings)} sections")
    print(f"✓ File exists after load: {settings_file.exists()}")
    
    # Verify defaults
    assert current_settings['timezone']['data_timezone'] == 'UTC-5'
    assert current_settings['ui_preferences']['default_timeframe'] == 'H1'
    assert current_settings['cache']['cache_compression'] == 'lz4'
    print("✓ Default values verified")
    
    # Scenario 2: First save creates complete file
    print("\n📋 Scenario 2: First save creates complete file")
    
    test_data = {
        'timezone': {
            'data_timezone': 'UTC-5',
            'display_timezone': 'UTC+7'
        }
    }
    
    success = panel._save_settings(test_data)
    assert success, "Save should succeed"
    assert settings_file.exists(), "File should be created"
    
    # Check file content
    with open(settings_file, 'r') as f:
        saved_settings = json.load(f)
    
    required_sections = ['version', 'timezone', 'market_sessions', 'data_loading', 'cache', 'ui_preferences']
    for section in required_sections:
        assert section in saved_settings, f"Missing section: {section}"
    
    print(f"✓ Complete file created with {len(saved_settings)} sections")
    print(f"✓ File size: {settings_file.stat().st_size} bytes")
    
    # Scenario 3: Subsequent saves merge with existing
    print("\n📋 Scenario 3: Subsequent saves merge with existing")
    
    # Save different data
    test_data2 = {
        'ui_preferences': {
            'default_timeframe': 'H4',
            'chart_style': 'ohlc'
        }
    }
    
    success2 = panel._save_settings(test_data2)
    assert success2, "Second save should succeed"
    
    # Check merged content
    with open(settings_file, 'r') as f:
        merged_settings = json.load(f)
    
    # Should have merged values
    assert merged_settings['ui_preferences']['default_timeframe'] == 'H4'
    assert merged_settings['ui_preferences']['chart_style'] == 'ohlc'
    # Should preserve other values
    assert merged_settings['timezone']['data_timezone'] == 'UTC-5'
    assert merged_settings['cache']['cache_compression'] == 'lz4'
    
    print("✓ Settings merged correctly")
    
    # Scenario 4: Delete and auto-recreate through interface
    print("\n📋 Scenario 4: Delete and auto-recreate through interface")
    
    settings_file.unlink()
    print("✓ File deleted")
    
    # Create new panel (simulates opening settings panel in UI)
    panel2 = SimpleSettingsPanel()
    settings2 = panel2._load_current_settings()
    
    # Should load defaults even without file
    assert settings2['timezone']['data_timezone'] == 'UTC-5'
    assert settings2['ui_preferences']['default_timeframe'] == 'H1'  # Back to default
    print("✓ Defaults loaded after deletion")
    
    # Save should recreate complete file
    panel2._save_settings({'timezone': {'data_timezone': 'UTC-5'}})
    assert settings_file.exists()
    
    with open(settings_file, 'r') as f:
        recreated_settings = json.load(f)
    
    assert len(recreated_settings) >= 6  # All sections
    print("✓ Complete file recreated")
    
    return True

def test_streamlit_integration():
    """Test that auto-creation works with Streamlit interface."""
    print("\n🧪 Testing Streamlit Integration")
    print("=" * 50)
    
    project_dir = Path(__file__).parent.parent
    settings_file = project_dir / 'config' / 'user_settings.json'
    
    # Delete file to simulate fresh start
    if settings_file.exists():
        settings_file.unlink()
        print("✓ Settings file deleted")
    
    print("📋 Simulation: User opens settings panel for first time")
    print("  1. User opens Streamlit app")
    print("  2. User clicks 'Phase 5.1 Settings'")
    print("  3. Settings panel loads defaults (no file exists)")
    print("  4. User changes a setting and clicks save")
    print("  5. Complete file should be created")
    
    # Simulate this flow
    import sys
    sys.path.append(str(project_dir.parent))
    from streamtrade.gui.components.simple_settings_panel import SimpleSettingsPanel
    
    # Step 3: Panel loads defaults
    panel = SimpleSettingsPanel()
    settings = panel._load_current_settings()
    print(f"✓ Step 3: Defaults loaded ({len(settings)} sections)")
    
    # Step 4: User saves a setting
    user_change = {
        'timezone': {
            'display_timezone': 'UTC+8'  # User changes timezone
        }
    }
    
    success = panel._save_settings(user_change)
    print(f"✓ Step 4: User save successful: {success}")
    
    # Step 5: Check complete file created
    assert settings_file.exists()
    with open(settings_file, 'r') as f:
        final_settings = json.load(f)
    
    # Should have user's change
    assert final_settings['timezone']['display_timezone'] == 'UTC+8'
    # Should have all defaults
    assert final_settings['ui_preferences']['default_timeframe'] == 'H1'
    assert final_settings['cache']['cache_compression'] == 'lz4'
    
    print(f"✓ Step 5: Complete file created with user's change")
    print(f"  File sections: {list(final_settings.keys())}")
    print(f"  User's timezone: {final_settings['timezone']['display_timezone']}")
    print(f"  Default timeframe: {final_settings['ui_preferences']['default_timeframe']}")
    
    return True

if __name__ == "__main__":
    print("🚀 Auto-Creation Test Suite")
    print("=" * 60)
    
    try:
        test1_passed = test_auto_creation_scenarios()
        test2_passed = test_streamlit_integration()
        
        print("\n" + "=" * 60)
        if test1_passed and test2_passed:
            print("✅ ALL AUTO-CREATION TESTS PASSED!")
            print("\n📋 Auto-Creation System Status:")
            print("  ✓ File auto-creates when deleted")
            print("  ✓ Complete defaults included on first save")
            print("  ✓ Subsequent saves merge correctly")
            print("  ✓ Streamlit interface integration works")
            print("  ✓ User changes preserved with defaults")
            print("\n🎯 Auto-Creation System is FULLY FUNCTIONAL!")
            print("\n💡 What happens when file is deleted:")
            print("  1. Settings panel loads defaults from code")
            print("  2. User can use interface normally")
            print("  3. First save creates complete file with all sections")
            print("  4. All default values are preserved")
            print("  5. User changes are applied on top of defaults")
        else:
            print("❌ SOME TESTS FAILED!")
    
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
