"""
Test suite for Phase 5.4: Smart Disk Cache System implementation.
Tests Smart Disk Cache, LRU eviction, indicator caching, and integration.
"""

import unittest
import pandas as pd
import numpy as np
import tempfile
import shutil
from datetime import datetime, timedelta
from pathlib import Path

# Import the modules to test
import sys
sys.path.append(str(Path(__file__).parent.parent))

from streamtrade.cache.disk_cache import SmartDiskCache
from streamtrade.cache.lru_manager import LRUCacheManager
from streamtrade.cache.cache_metadata import CacheMetadata
from streamtrade.cache.indicator_cache import IndicatorCache
from streamtrade.data.enhanced_data_manager import EnhancedDataManager
from streamtrade.config.logging_config import get_logger

logger = get_logger(__name__)


class TestSmartDiskCache(unittest.TestCase):
    """Test Smart Disk Cache core functionality."""
    
    def setUp(self):
        """Set up test environment."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.cache_dir = self.temp_dir / 'test_cache'
        self.disk_cache = SmartDiskCache(cache_dir=self.cache_dir, max_size_gb=0.1)  # 100MB for testing
        
        # Create sample data
        self.sample_data = self._create_sample_data()
        self.start_date = datetime(2024, 1, 1)
        self.end_date = datetime(2024, 1, 5)
    
    def tearDown(self):
        """Clean up test environment."""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def _create_sample_data(self) -> pd.DataFrame:
        """Create sample OHLCV data for testing."""
        dates = pd.date_range('2024-01-01', periods=1000, freq='1min')
        data = pd.DataFrame({
            'Open': np.random.uniform(1.0, 2.0, len(dates)),
            'High': np.random.uniform(1.5, 2.5, len(dates)),
            'Low': np.random.uniform(0.5, 1.5, len(dates)),
            'Close': np.random.uniform(1.0, 2.0, len(dates)),
            'Volume': np.random.randint(1000, 10000, len(dates))
        }, index=dates)
        return data
    
    def test_cache_directory_creation(self):
        """Test that cache directories are created properly."""
        self.assertTrue(self.cache_dir.exists())
        self.assertTrue((self.cache_dir / 'data' / 'm1_base').exists())
        self.assertTrue((self.cache_dir / 'data' / 'timeframes').exists())
        self.assertTrue((self.cache_dir / 'data' / 'indicators').exists())
        self.assertTrue((self.cache_dir / 'styles').exists())
        self.assertTrue((self.cache_dir / 'metadata').exists())
    
    def test_m1_data_storage_and_retrieval(self):
        """Test M1 data storage and retrieval."""
        # Store M1 data
        cache_key = self.disk_cache.store_m1_data(
            'EURUSD', self.sample_data, self.start_date, self.end_date
        )

        self.assertIsNotNone(cache_key)

        # Retrieve M1 data
        retrieved_data = self.disk_cache.load_data(cache_key)

        self.assertIsNotNone(retrieved_data)
        self.assertEqual(len(retrieved_data), len(self.sample_data))

        # Compare data values (allowing for minor differences due to Parquet serialization)
        pd.testing.assert_frame_equal(
            retrieved_data.reset_index(drop=True),
            self.sample_data.reset_index(drop=True),
            check_index_type=False,
            check_dtype=False
        )
    
    def test_timeframe_data_storage(self):
        """Test timeframe data storage with dependency tracking."""
        # Store M1 data first
        m1_cache_key = self.disk_cache.store_m1_data(
            'EURUSD', self.sample_data, self.start_date, self.end_date
        )
        
        # Create H1 data (sample conversion)
        h1_data = self.sample_data.resample('1h').agg({
            'Open': 'first',
            'High': 'max',
            'Low': 'min',
            'Close': 'last',
            'Volume': 'sum'
        }).dropna()
        
        # Store H1 data
        h1_cache_key = self.disk_cache.store_timeframe_data(
            'EURUSD', 'H1', h1_data, m1_cache_key
        )
        
        self.assertIsNotNone(h1_cache_key)
        
        # Verify dependency tracking
        dependents = self.disk_cache.metadata.find_dependents(m1_cache_key)
        self.assertIn(h1_cache_key, dependents)
    
    def test_cache_key_finding(self):
        """Test cache key finding functionality."""
        # Store M1 data
        cache_key = self.disk_cache.store_m1_data(
            'EURUSD', self.sample_data, self.start_date, self.end_date
        )
        
        # Find cache key
        found_key = self.disk_cache.find_m1_cache('EURUSD', self.start_date, self.end_date)
        self.assertEqual(found_key, cache_key)
        
        # Test with different date range (should not find)
        not_found = self.disk_cache.find_m1_cache(
            'EURUSD', 
            datetime(2024, 2, 1), 
            datetime(2024, 2, 5)
        )
        self.assertIsNone(not_found)
    
    def test_cache_stats(self):
        """Test cache statistics functionality."""
        # Store some data
        self.disk_cache.store_m1_data(
            'EURUSD', self.sample_data, self.start_date, self.end_date
        )
        
        stats = self.disk_cache.get_stats()
        
        self.assertIn('metadata', stats)
        self.assertIn('lru', stats)
        self.assertGreater(stats['metadata']['total_entries'], 0)
        self.assertGreater(stats['metadata']['total_size_mb'], 0)


class TestLRUCacheManager(unittest.TestCase):
    """Test LRU Cache Manager functionality."""
    
    def setUp(self):
        """Set up test environment."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.cache_dir = self.temp_dir / 'test_cache'
        self.cache_dir.mkdir(parents=True)
        
        # Very small cache size for testing eviction
        self.lru_manager = LRUCacheManager(self.cache_dir, max_size_gb=0.001)  # 1MB
        self.metadata = CacheMetadata(self.cache_dir / 'metadata')
    
    def tearDown(self):
        """Clean up test environment."""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_access_tracking(self):
        """Test access time tracking."""
        cache_key = 'test_key_1'
        
        # Record access
        self.lru_manager.record_access(cache_key)
        
        # Check if access was recorded
        self.assertIn(cache_key, self.lru_manager.access_log)
        self.assertIsInstance(self.lru_manager.access_log[cache_key], datetime)
    
    def test_lru_eviction_logic(self):
        """Test LRU eviction logic (without actual files)."""
        # This test focuses on the logic rather than file operations
        stats = self.lru_manager.get_stats()
        
        self.assertIn('max_size_gb', stats)
        self.assertIn('eviction_stats', stats)
        self.assertEqual(stats['max_size_gb'], 0.001)


class TestIndicatorCache(unittest.TestCase):
    """Test Indicator Cache functionality."""
    
    def setUp(self):
        """Set up test environment."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.cache_dir = self.temp_dir / 'test_cache'
        
        self.disk_cache = SmartDiskCache(cache_dir=self.cache_dir, max_size_gb=0.1)
        self.indicator_cache = IndicatorCache(self.disk_cache)
        
        # Sample indicator data
        self.indicator_data = pd.DataFrame({
            'sma_20': np.random.uniform(1.0, 2.0, 100),
            'sma_50': np.random.uniform(1.0, 2.0, 100)
        }, index=pd.date_range('2024-01-01', periods=100, freq='1min'))
        
        self.indicator_params = {'period': 20, 'type': 'sma'}
        self.style_config = {'color': '#FF0000', 'width': 2}
    
    def tearDown(self):
        """Clean up test environment."""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_indicator_calculation_caching(self):
        """Test indicator calculation caching."""
        data_cache_key = 'test_data_key'
        
        # Store indicator calculation
        indicator_key = self.indicator_cache.store_indicator_calculation(
            'SMA', self.indicator_params, data_cache_key, self.indicator_data
        )
        
        self.assertIsNotNone(indicator_key)
        
        # Load indicator calculation
        loaded_data = self.indicator_cache.load_indicator_calculation(
            'SMA', self.indicator_params, data_cache_key
        )
        
        self.assertIsNotNone(loaded_data)
        pd.testing.assert_frame_equal(
            loaded_data.reset_index(drop=True),
            self.indicator_data.reset_index(drop=True),
            check_index_type=False,
            check_dtype=False
        )
    
    def test_indicator_style_caching(self):
        """Test indicator style caching."""
        # Store style configuration
        style_key = self.indicator_cache.store_indicator_style(
            'SMA', self.style_config
        )
        
        self.assertIsNotNone(style_key)
        
        # Load style configuration
        loaded_style = self.indicator_cache.load_indicator_style(
            'SMA', self.style_config
        )
        
        self.assertIsNotNone(loaded_style)
        self.assertEqual(loaded_style, self.style_config)
    
    def test_indicator_cache_invalidation(self):
        """Test indicator cache invalidation."""
        data_cache_key = 'test_data_key'
        
        # Store indicator calculation
        self.indicator_cache.store_indicator_calculation(
            'SMA', self.indicator_params, data_cache_key, self.indicator_data
        )
        
        # Invalidate indicator cache
        removed_count = self.indicator_cache.invalidate_indicator('SMA', data_cache_key)
        
        self.assertGreater(removed_count, 0)
        
        # Try to load (should return None)
        loaded_data = self.indicator_cache.load_indicator_calculation(
            'SMA', self.indicator_params, data_cache_key
        )
        
        self.assertIsNone(loaded_data)
    
    def test_indicator_stats(self):
        """Test indicator cache statistics."""
        data_cache_key = 'test_data_key'
        
        # Store some indicator data
        self.indicator_cache.store_indicator_calculation(
            'SMA', self.indicator_params, data_cache_key, self.indicator_data
        )
        self.indicator_cache.store_indicator_style('SMA', self.style_config)
        
        stats = self.indicator_cache.get_indicator_stats()
        
        self.assertIn('total_indicators', stats)
        self.assertIn('total_styles', stats)
        self.assertIn('by_indicator', stats)
        self.assertGreater(stats['total_indicators'], 0)


class TestEnhancedDataManagerIntegration(unittest.TestCase):
    """Test Enhanced Data Manager integration with Smart Disk Cache."""
    
    def setUp(self):
        """Set up test environment."""
        # Note: This test uses the actual EnhancedDataManager
        # In a real test environment, you might want to mock the data loader
        self.data_manager = EnhancedDataManager()
    
    def test_disk_cache_initialization(self):
        """Test that disk cache is properly initialized."""
        if self.data_manager.enable_disk_cache:
            self.assertIsNotNone(self.data_manager.disk_cache)
            self.assertIsNotNone(self.data_manager.indicator_cache)
        else:
            self.assertIsNone(self.data_manager.disk_cache)
            self.assertIsNone(self.data_manager.indicator_cache)
    
    def test_cache_info_integration(self):
        """Test comprehensive cache info."""
        cache_info = self.data_manager.get_cache_info()
        
        self.assertIn('memory_cache', cache_info)
        self.assertIn('disk_cache', cache_info)
        self.assertIn('indicator_cache', cache_info)
        self.assertIn('disk_cache_enabled', cache_info)
    
    def test_indicator_cache_methods(self):
        """Test indicator cache methods."""
        # Test with dummy data
        dummy_params = {'period': 20}
        dummy_data = pd.DataFrame({'value': [1, 2, 3]})
        dummy_style = {'color': '#FF0000'}
        
        # These should not raise errors even if disk cache is disabled
        cache_key = self.data_manager.cache_indicator_calculation(
            'TEST', dummy_params, 'dummy_data_key', dummy_data
        )
        
        style_key = self.data_manager.cache_indicator_style('TEST', dummy_style)
        
        # Methods should return None if disk cache is disabled, or valid keys if enabled
        if self.data_manager.enable_disk_cache:
            self.assertIsNotNone(cache_key)
            self.assertIsNotNone(style_key)
        else:
            self.assertIsNone(cache_key)
            self.assertIsNone(style_key)


def run_phase5_4_tests():
    """Run all Phase 5.4 Smart Disk Cache tests."""
    print("🧪 Running Phase 5.4: Smart Disk Cache System Tests")
    print("=" * 60)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestSmartDiskCache,
        TestLRUCacheManager,
        TestIndicatorCache,
        TestEnhancedDataManagerIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print(f"📊 Test Results Summary:")
    print(f"   Tests run: {result.testsRun}")
    print(f"   Failures: {len(result.failures)}")
    print(f"   Errors: {len(result.errors)}")
    print(f"   Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print(f"\n❌ Failures:")
        for test, traceback in result.failures:
            print(f"   - {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print(f"\n🚨 Errors:")
        for test, traceback in result.errors:
            print(f"   - {test}: {traceback.split('Error:')[-1].strip()}")
    
    if not result.failures and not result.errors:
        print("\n✅ All tests passed! Smart Disk Cache System is working correctly.")
    
    return result.wasSuccessful()


if __name__ == '__main__':
    success = run_phase5_4_tests()
    exit(0 if success else 1)
