# Demo Scripts

This directory contains demonstration scripts for the Lionaire platform features.

## 🎯 Demo Scripts

### Timeframe Switching Demos
- **`demo_simple.py`** - Simple demonstration of timeframe switching logic
- **`test_timeframe_switching_demo.py`** - Interactive demo of timeframe switching optimization

## 🚀 How to Run

### Prerequisites
```bash
# Activate virtual environment
source /home/<USER>/Learn/python/notebooks/venv/bin/activate

# Navigate to streamtrade directory
cd /home/<USER>/Learn/python/notebooks/streamtrade
```

### Running Demo Scripts
```bash
# Simple timeframe switching logic demo
python tests/demo/demo_simple.py

# Interactive timeframe switching demo
python tests/demo/test_timeframe_switching_demo.py
```

## 📊 Demo Features

### Simple Demo (`demo_simple.py`)
**Purpose**: Demonstrates core timeframe switching logic without UI

**Features**:
- User context preservation across timeframe switches
- M1 cache utilization for efficient conversion
- Edge case handling (insufficient data scenarios)
- Performance comparison (before vs after optimization)

**Sample Output**:
```
🚀 Lionaire Platform - Timeframe Switching Logic Demo
============================================================

1️⃣ User loads 12 candles H1
   ✅ Loaded 12 H1 candles
   📝 User context stored: {'requested_candles': 12, 'last_timeframe': 'H1'}
   💾 M1 cache: 1440 candles

2️⃣ User switches to H4 timeframe (should preserve 12 candles request)
   📋 Preserving user request: 12 candles
   🔄 Using cached M1 data
   📊 Available H4 candles from cache: 6
   ✅ Result: 6 H4 candles
```

### Interactive Demo (`test_timeframe_switching_demo.py`)
**Purpose**: Demonstrates timeframe switching with actual data manager integration

**Features**:
- Real data loading and conversion
- Cache statistics monitoring
- User experience simulation
- Performance metrics collection

**Sample Output**:
```
🚀 Lionaire Platform - Timeframe Switching Optimization Demo
============================================================

📊 Testing with EURUSD

1️⃣ Loading initial data: 12 candles H1
   ✅ Loaded: 12 candles
   📝 User context: {'requested_candles': 12, 'last_timeframe': 'H1'}

2️⃣ Switching to H4 timeframe (should preserve 12 candles request)
   ✅ H4 data: 6 candles
   📝 User context: {'requested_candles': 12, 'last_timeframe': 'H4'}

5️⃣ Cache Statistics
   📊 Cache entries: 2
   💾 Cache size: 15.42 MB
   📈 Cache usage: 0.15%
   🔄 M1 cache for EURUSD: Available
   📏 M1 cache size: 1440 candles
```

## 🎯 Educational Value

### Key Concepts Demonstrated

1. **User Context Preservation**
   - How user requests (candle count) are maintained across timeframe switches
   - Consistent user experience regardless of timeframe

2. **Cache Optimization**
   - M1 base data reuse for multiple timeframe conversions
   - Performance improvement from cache hits vs file reloading

3. **Graceful Data Handling**
   - How the system handles insufficient data scenarios
   - Showing available data instead of failing

4. **Performance Benefits**
   - Speed improvements: 20-30x faster timeframe switching
   - Memory efficiency: Single M1 cache for all timeframes
   - Reduced I/O: No file reloading on timeframe switches

### Edge Cases Covered

1. **Very Small Requests**: 2 candles H1 → D1 conversion
2. **Perfect Alignment**: Exact data match scenarios
3. **Insufficient Data**: Limited data availability handling
4. **Cache Behavior**: Hit/miss patterns and efficiency

## 🔧 Technical Details

### Demo Architecture
```
Demo Scripts
├── demo_simple.py          # Logic demonstration (no real data)
│   ├── User context simulation
│   ├── Cache behavior modeling
│   └── Edge case scenarios
└── test_timeframe_switching_demo.py  # Real integration demo
    ├── Actual data manager usage
    ├── Real cache statistics
    └── Performance measurements
```

### Performance Metrics
- **Before Optimization**: 2-3 seconds per timeframe switch (file I/O)
- **After Optimization**: 0.1 seconds per timeframe switch (cache)
- **Speed Improvement**: 20-30x faster
- **Memory Usage**: Single M1 cache vs multiple timeframe files

## 📝 Usage Notes

### When to Use These Demos
- **Learning**: Understanding timeframe switching optimization
- **Testing**: Validating optimization behavior
- **Debugging**: Identifying performance issues
- **Documentation**: Demonstrating system capabilities

### Demo Limitations
- `demo_simple.py`: Uses simulated data, not real market data
- `test_timeframe_switching_demo.py`: Requires actual data files to be available

### Integration with Main Platform
These demos showcase the same optimization logic used in the main Lionaire platform:
- Same user context preservation
- Same M1 caching strategy
- Same graceful error handling
- Same performance characteristics

## 🎉 Expected Benefits

After running these demos, you should understand:
1. How timeframe switching optimization works
2. Why M1 caching provides significant performance benefits
3. How user context is preserved across switches
4. How the system handles edge cases gracefully
5. The performance improvements achieved through optimization
