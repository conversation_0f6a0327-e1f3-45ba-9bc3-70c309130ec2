"""
Integration test for Phase 5.1 implementation.
Tests integration with existing Lionaire platform components.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test that all new components can be imported."""
    print("Testing imports...")
    
    try:
        # Test minimal components (should work)
        from streamtrade.config.minimal_settings import MinimalSettings
        print("✓ MinimalSettings imported")
        
        from streamtrade.data.minimal_session_converter import MinimalSessionConverter
        print("✓ MinimalSessionConverter imported")
        
        # Test that they work
        settings = MinimalSettings()
        converter = MinimalSessionConverter()
        
        print("✓ Components instantiated successfully")
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False

def test_settings_functionality():
    """Test settings system functionality."""
    print("\nTesting settings functionality...")
    
    try:
        from streamtrade.config.minimal_settings import MinimalSettings
        
        settings = MinimalSettings()
        
        # Test basic access
        data_tz = settings.get('timezone.data_timezone')
        assert data_tz == 'UTC-5', f"Expected UTC-5, got {data_tz}"
        print(f"✓ Data timezone: {data_tz}")
        
        # Test forex detection
        assert settings.is_forex_pair('EURUSD') == True
        assert settings.is_forex_pair('XAUUSD') == False
        print("✓ Forex pair detection works")
        
        # Test market open times
        forex_open = settings.get_market_open_time('EURUSD')
        gold_open = settings.get_market_open_time('XAUUSD')
        assert forex_open == '16:00'
        assert gold_open == '17:00'
        print(f"✓ Market open times: Forex={forex_open}, Gold={gold_open}")
        
        return True
        
    except Exception as e:
        print(f"❌ Settings test failed: {e}")
        return False

def test_session_converter():
    """Test session-aware converter functionality."""
    print("\nTesting session converter...")
    
    try:
        from streamtrade.data.minimal_session_converter import MinimalSessionConverter
        from datetime import datetime
        
        converter = MinimalSessionConverter()
        
        # Test session boundaries
        test_date = datetime(2024, 1, 15)  # Monday
        
        # Forex sessions
        forex_sessions = converter.get_session_boundaries('EURUSD', test_date)
        assert forex_sessions['is_forex'] == True
        assert forex_sessions['market_open'] == '16:00'
        assert len(forex_sessions['h4_sessions']) == 6
        print("✓ Forex session boundaries calculated")
        
        # Non-forex sessions  
        gold_sessions = converter.get_session_boundaries('XAUUSD', test_date)
        assert gold_sessions['is_forex'] == False
        assert gold_sessions['market_open'] == '17:00'
        assert len(gold_sessions['h4_sessions']) == 6
        print("✓ Non-forex session boundaries calculated")
        
        # Verify 1-hour offset
        forex_start = forex_sessions['h4_sessions'][0]['start']
        gold_start = gold_sessions['h4_sessions'][0]['start']
        time_diff = (gold_start - forex_start).total_seconds() / 3600
        assert time_diff == 1.0, f"Expected 1 hour difference, got {time_diff}"
        print("✓ 1-hour offset between Forex and Non-Forex verified")
        
        return True
        
    except Exception as e:
        print(f"❌ Session converter test failed: {e}")
        return False

def test_data_conversion():
    """Test actual data conversion."""
    print("\nTesting data conversion...")
    
    try:
        import pandas as pd
        import numpy as np
        from datetime import datetime, timedelta
        from streamtrade.data.minimal_session_converter import MinimalSessionConverter
        
        converter = MinimalSessionConverter()
        
        # Create sample M1 data (4 hours worth)
        start_time = datetime(2024, 1, 15, 16, 0)  # Monday 16:00
        times = [start_time + timedelta(minutes=i) for i in range(240)]
        
        # Create realistic OHLCV data
        base_price = 1.0800
        data = []
        
        for i, timestamp in enumerate(times):
            open_price = base_price + np.random.uniform(-0.0005, 0.0005)
            close_price = open_price + np.random.uniform(-0.0005, 0.0005)
            high_price = max(open_price, close_price) + np.random.uniform(0, 0.0003)
            low_price = min(open_price, close_price) - np.random.uniform(0, 0.0003)
            
            data.append({
                'open': round(open_price, 5),
                'high': round(high_price, 5),
                'low': round(low_price, 5),
                'close': round(close_price, 5),
                'volume': np.random.randint(50, 200)
            })
            
            base_price = close_price
        
        m1_df = pd.DataFrame(data, index=pd.DatetimeIndex(times))
        print(f"✓ Created {len(m1_df)} M1 candles")
        
        # Convert to H4
        h4_df = converter.convert_to_h4(m1_df, 'EURUSD')
        print(f"✓ Converted to {len(h4_df)} H4 candles")
        
        # Verify conversion
        assert len(h4_df) == 1, f"Expected 1 H4 candle, got {len(h4_df)}"
        
        if not h4_df.empty:
            h4_candle = h4_df.iloc[0]
            
            # Verify OHLC logic
            assert h4_candle['open'] == m1_df['open'].iloc[0]
            assert h4_candle['close'] == m1_df['close'].iloc[-1]
            assert h4_candle['high'] == m1_df['high'].max()
            assert h4_candle['low'] == m1_df['low'].min()
            
            print(f"✓ H4 OHLC verified: O={h4_candle['open']:.5f} H={h4_candle['high']:.5f} L={h4_candle['low']:.5f} C={h4_candle['close']:.5f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Data conversion test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_backward_compatibility():
    """Test that existing system still works."""
    print("\nTesting backward compatibility...")
    
    try:
        # Test that old imports still work
        from streamtrade.data import DataManager, DataLoader, TimeframeConverter
        print("✓ Original components still importable")
        
        # Test that settings still work
        from streamtrade.config.settings import settings
        print("✓ Original settings still work")
        
        # Test that new components are available
        from streamtrade.data import enhanced_data_manager, data_manager
        print("✓ Both old and new data managers available")
        
        return True
        
    except Exception as e:
        print(f"❌ Backward compatibility test failed: {e}")
        return False

def main():
    """Run all integration tests."""
    print("🧪 Phase 5.1 Integration Tests")
    print("=" * 40)
    
    tests = [
        test_imports,
        test_settings_functionality,
        test_session_converter,
        test_data_conversion,
        test_backward_compatibility
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ {test.__name__} failed")
        except Exception as e:
            print(f"❌ {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 40)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All integration tests passed!")
        print("\n🎯 Phase 5.1 Implementation Status:")
        print("  ✓ User Settings System")
        print("  ✓ Session-Aware Timeframe Conversion")
        print("  ✓ Enhanced Data Management")
        print("  ✓ Forex vs Non-Forex Support")
        print("  ✓ Backward Compatibility")
        print("\n🚀 Ready for Phase 5.2 - Smart Disk Cache System")
    else:
        print(f"❌ {total - passed} tests failed")
        print("🔧 Issues need to be resolved before proceeding")

if __name__ == "__main__":
    main()
