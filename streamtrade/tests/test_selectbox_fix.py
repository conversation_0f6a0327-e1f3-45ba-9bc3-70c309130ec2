"""
Test selectbox error fix.
"""

import json
from pathlib import Path

def test_selectbox_error_fix():
    """Test that selectbox errors are fixed."""
    print("🧪 Testing Selectbox Error Fix")
    print("=" * 40)
    
    # Test 1: Create settings with problematic values
    project_dir = Path(__file__).parent.parent
    config_dir = project_dir / 'config'
    settings_file = config_dir / 'user_settings.json'
    
    # Create settings with values that might not be in selectbox options
    test_settings = {
        'version': '1.0.0',
        'timezone': {
            'data_timezone': 'UTC-6',  # This was causing the error
            'display_timezone': 'UTC+8'
        },
        'market_sessions': {
            'forex_open': '16:00',
            'non_forex_open': '17:00',
            'non_forex_symbols': ['XAUUSD', 'SPXUSD', 'NSXUSD']
        },
        'cache': {
            'cache_compression': 'brotli'  # Not in original options
        },
        'ui_preferences': {
            'default_timeframe': 'H2',  # Not in original options
            'chart_style': 'heikin_ashi'  # Not in original options
        }
    }
    
    # Save test settings
    try:
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(test_settings, f, indent=2)
        print("✓ Test settings with problematic values created")
    except Exception as e:
        print(f"❌ Failed to create test settings: {e}")
        return False
    
    # Test 2: Try to load SimpleSettingsPanel with these settings
    try:
        import sys
        sys.path.append(str(Path(__file__).parent.parent.parent))
        
        from streamtrade.gui.components.simple_settings_panel import SimpleSettingsPanel
        print("✓ SimpleSettingsPanel imported")
        
        # Create panel instance
        panel = SimpleSettingsPanel()
        print("✓ SimpleSettingsPanel instance created")
        
        # Load current settings (this should not crash)
        current_settings = panel._load_current_settings()
        print("✓ Settings loaded without error")
        
        # Test safe selectbox function
        test_options = ["UTC-5", "UTC-4", "UTC-3", "UTC", "UTC+1", "UTC+2"]
        
        # Test with value in options
        result1 = panel._safe_selectbox("Test1", test_options, "UTC-5", "UTC-5", "Test help")
        print(f"✓ Safe selectbox with valid value: {result1}")
        
        # Test with value NOT in options (this was causing the error)
        result2 = panel._safe_selectbox("Test2", test_options, "UTC-6", "UTC-5", "Test help")
        print(f"✓ Safe selectbox with invalid value: {result2}")
        
        # Test with empty value
        result3 = panel._safe_selectbox("Test3", test_options, "", "UTC-5", "Test help")
        print(f"✓ Safe selectbox with empty value: {result3}")
        
        return True
        
    except Exception as e:
        print(f"❌ SimpleSettingsPanel test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_settings_file_validation():
    """Test that settings file is properly validated."""
    print("\n🧪 Testing Settings File Validation")
    print("=" * 40)
    
    try:
        project_dir = Path(__file__).parent.parent
        config_dir = project_dir / 'config'
        settings_file = config_dir / 'user_settings.json'
        
        # Load current settings
        if settings_file.exists():
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)
            
            print("✓ Settings file loaded successfully")
            
            # Check structure
            required_keys = ['timezone', 'market_sessions', 'cache', 'ui_preferences']
            for key in required_keys:
                if key in settings:
                    print(f"✓ {key} section exists")
                else:
                    print(f"⚠️ {key} section missing")
            
            # Check specific values
            data_tz = settings.get('timezone', {}).get('data_timezone', 'Unknown')
            display_tz = settings.get('timezone', {}).get('display_timezone', 'Unknown')
            
            print(f"✓ Data timezone: {data_tz}")
            print(f"✓ Display timezone: {display_tz}")
            
            return True
        else:
            print("ℹ️ Settings file does not exist yet")
            return True
            
    except Exception as e:
        print(f"❌ Settings file validation failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Selectbox Error Fix Verification")
    print("=" * 50)
    
    # Run tests
    test1_passed = test_selectbox_error_fix()
    test2_passed = test_settings_file_validation()
    
    print("\n" + "=" * 50)
    if test1_passed and test2_passed:
        print("✅ ALL TESTS PASSED!")
        print("\n📋 Error Fix Status:")
        print("  ✓ Selectbox error handling implemented")
        print("  ✓ Safe selectbox function working")
        print("  ✓ Settings file validation working")
        print("  ✓ Problematic values handled gracefully")
        print("\n🎯 Settings panel should now work without errors!")
        print("  • Open browser to http://localhost:8502")
        print("  • Click 'Phase 5.1 Settings' in sidebar")
        print("  • Settings should load without 'not in list' errors")
    else:
        print("❌ SOME TESTS FAILED!")
        print("🔧 Please check the errors above")
