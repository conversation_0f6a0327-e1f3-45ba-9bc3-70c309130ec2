"""
Test timeframe switching optimization and user context preservation.
"""

import pytest
import pandas as pd
from datetime import datetime, timedelta
import sys
from pathlib import Path

# Add the parent directory to the path to import streamtrade modules
sys.path.append(str(Path(__file__).parent.parent.parent))

from streamtrade.data.data_manager import DataManager
from streamtrade.visualization.chart_viewer import <PERSON><PERSON>ie<PERSON>
from streamtrade.config.settings import settings


class TestTimeframeSwitching:
    """Test timeframe switching optimization."""
    
    def setup_method(self):
        """Setup test environment."""
        self.data_manager = DataManager()
        self.chart_viewer = ChartViewer()
        self.test_pair = "EURUSD"
        
    def test_user_context_preservation(self):
        """Test that user context is preserved across timeframe switches."""
        # Load initial data with specific candle count
        requested_candles = 12
        initial_timeframe = "H1"
        
        # Load data and check user context is stored
        data = self.data_manager.get_data(
            pair=self.test_pair,
            timeframe=initial_timeframe,
            max_candles=requested_candles,
            preserve_user_request=True
        )
        
        assert data is not None, "Should load initial data"
        
        # Check user context is stored
        user_context = self.data_manager.get_user_context(self.test_pair)
        assert user_context is not None, "User context should be stored"
        assert user_context['requested_candles'] == requested_candles
        assert user_context['last_timeframe'] == initial_timeframe
        
    def test_timeframe_switch_preserves_candle_count(self):
        """Test that timeframe switching preserves the requested candle count."""
        # Load initial data
        requested_candles = 12
        initial_timeframe = "H1"
        
        data = self.data_manager.get_data(
            pair=self.test_pair,
            timeframe=initial_timeframe,
            max_candles=requested_candles,
            preserve_user_request=True
        )
        
        assert data is not None, "Should load initial data"
        assert len(data) <= requested_candles, "Should respect max_candles limit"
        
        # Switch to different timeframe
        new_timeframe = "H4"
        switched_data = self.data_manager.get_data_for_timeframe_switch(
            pair=self.test_pair,
            new_timeframe=new_timeframe
        )
        
        assert switched_data is not None, "Should load switched data"
        
        # Check that the system tried to preserve the candle count
        # For H4, we might get fewer candles due to data availability
        user_context = self.data_manager.get_user_context(self.test_pair)
        assert user_context['requested_candles'] == requested_candles
        assert user_context['last_timeframe'] == new_timeframe
        
    def test_insufficient_data_handling(self):
        """Test handling when cached data is insufficient for requested timeframe."""
        # Load small amount of H1 data
        requested_candles = 2
        initial_timeframe = "H1"
        
        data = self.data_manager.get_data(
            pair=self.test_pair,
            timeframe=initial_timeframe,
            max_candles=requested_candles,
            preserve_user_request=True
        )
        
        assert data is not None, "Should load initial data"
        
        # Try to switch to D1 (which needs much more M1 data)
        new_timeframe = "D1"
        switched_data = self.data_manager.get_data_for_timeframe_switch(
            pair=self.test_pair,
            new_timeframe=new_timeframe
        )
        
        # Should return whatever data is available, not fail
        if switched_data is not None:
            # If we get data, it should be limited by what's available
            assert len(switched_data) <= requested_candles
        # If no data, that's also acceptable for insufficient data scenario
        
    def test_chart_viewer_timeframe_switching(self):
        """Test ChartViewer timeframe switching integration."""
        # Load initial data through ChartViewer
        success = self.chart_viewer.load_data(
            pair=self.test_pair,
            timeframe="H1",
            max_candles=10
        )
        
        assert success, "Should load initial data"
        assert self.chart_viewer.current_pair == self.test_pair
        assert self.chart_viewer.current_timeframe == "H1"
        
        # Get data info
        data_info = self.chart_viewer.get_data_info()
        assert data_info['pair'] == self.test_pair
        assert data_info['timeframe'] == "H1"
        assert data_info['candles_loaded'] > 0
        
        # Switch timeframe
        chart_fig = self.chart_viewer.change_timeframe("H4")
        assert chart_fig is not None, "Should return chart figure"
        assert self.chart_viewer.current_timeframe == "H4"
        
        # Check data info after switch
        data_info_after = self.chart_viewer.get_data_info()
        assert data_info_after['timeframe'] == "H4"
        
    def test_m1_cache_utilization(self):
        """Test that M1 cache is properly utilized for timeframe switching."""
        # Load initial data to populate M1 cache
        data = self.data_manager.get_data(
            pair=self.test_pair,
            timeframe="H1",
            max_candles=24,  # 24 hours = 1 day worth of H1 data
            preserve_user_request=True
        )
        
        assert data is not None, "Should load initial data"
        
        # Check if M1 cache is populated
        assert self.test_pair in self.data_manager._m1_base_cache, "M1 cache should be populated"
        
        # Switch to different timeframe using cached data
        switched_data = self.data_manager.get_data_for_timeframe_switch(
            pair=self.test_pair,
            new_timeframe="M30"
        )
        
        assert switched_data is not None, "Should load switched data from cache"
        
    def test_cache_stats_and_cleanup(self):
        """Test cache statistics and cleanup functionality."""
        # Load some data to populate cache
        self.data_manager.get_data(
            pair=self.test_pair,
            timeframe="H1",
            max_candles=100
        )
        
        # Get cache stats
        cache_stats = self.data_manager.get_cache_stats()
        assert 'entries' in cache_stats
        assert 'total_size_mb' in cache_stats
        assert cache_stats['entries'] > 0
        
        # Clear cache
        self.data_manager.clear_cache()
        
        # Check cache is cleared
        cache_stats_after = self.data_manager.get_cache_stats()
        assert cache_stats_after['entries'] == 0


if __name__ == "__main__":
    # Run tests
    test_instance = TestTimeframeSwitching()
    
    print("Testing timeframe switching optimization...")
    
    try:
        test_instance.setup_method()
        test_instance.test_user_context_preservation()
        print("✅ User context preservation test passed")
        
        test_instance.setup_method()
        test_instance.test_timeframe_switch_preserves_candle_count()
        print("✅ Candle count preservation test passed")
        
        test_instance.setup_method()
        test_instance.test_insufficient_data_handling()
        print("✅ Insufficient data handling test passed")
        
        test_instance.setup_method()
        test_instance.test_chart_viewer_timeframe_switching()
        print("✅ ChartViewer integration test passed")
        
        test_instance.setup_method()
        test_instance.test_m1_cache_utilization()
        print("✅ M1 cache utilization test passed")
        
        test_instance.setup_method()
        test_instance.test_cache_stats_and_cleanup()
        print("✅ Cache stats and cleanup test passed")
        
        print("\n🎉 All timeframe switching tests passed!")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
