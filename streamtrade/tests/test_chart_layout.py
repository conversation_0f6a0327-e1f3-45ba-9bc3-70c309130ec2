"""
Test chart layout improvements and axis label removal.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from streamtrade.visualization.plotly_charts import PlotlyCharts
from streamtrade.indicators.base import IndicatorResult


class TestChartLayoutImprovements:
    """Test suite for chart layout improvements."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.plotly_charts = PlotlyCharts()
        
        # Create sample data
        dates = pd.date_range(start='2023-01-01', periods=100, freq='1H')
        np.random.seed(42)
        
        # Generate realistic OHLCV data
        close_prices = 100 + np.cumsum(np.random.randn(100) * 0.1)
        
        self.sample_data = pd.DataFrame({
            'open': close_prices + np.random.randn(100) * 0.05,
            'high': close_prices + np.abs(np.random.randn(100) * 0.1),
            'low': close_prices - np.abs(np.random.randn(100) * 0.1),
            'close': close_prices,
            'volume': np.random.randint(1000, 10000, 100)
        }, index=dates)
    
    def test_axis_labels_removed(self):
        """Test that axis labels are properly removed from charts."""
        # Create chart
        fig = self.plotly_charts.create_candlestick_chart(
            data=self.sample_data,
            title="Test Chart"
        )
        
        # Check that axis titles are empty
        assert fig.layout.xaxis.title.text == "", "X-axis title should be empty"
        assert fig.layout.yaxis.title.text == "", "Y-axis title should be empty"
    
    def test_subplot_titles_removed(self):
        """Test that subplot titles are properly removed."""
        # Create chart
        fig = self.plotly_charts.create_candlestick_chart(
            data=self.sample_data,
            title="Test Chart"
        )
        
        # Check subplot titles
        if hasattr(fig.layout, 'annotations'):
            for annotation in fig.layout.annotations:
                # Subplot titles should be empty
                if annotation.text and annotation.text.strip():
                    assert annotation.text.strip() == "", f"Subplot title should be empty, got: {annotation.text}"
    
    def test_chart_functionality_preserved(self):
        """Test that chart functionality is preserved after layout changes."""
        # Create chart
        fig = self.plotly_charts.create_candlestick_chart(
            data=self.sample_data,
            title="Test Chart"
        )
        
        # Check that chart has data
        assert len(fig.data) > 0, "Chart should have data traces"
        
        # Check that candlestick trace exists
        candlestick_traces = [trace for trace in fig.data if trace.type == 'candlestick']
        assert len(candlestick_traces) > 0, "Chart should have candlestick trace"
        
        # Check that layout is properly configured
        assert fig.layout.height is not None, "Chart should have height"
        assert fig.layout.width is not None, "Chart should have width"
    
    def test_simple_line_chart_labels_removed(self):
        """Test that simple line chart also has labels removed."""
        # Create sample series
        sample_series = self.sample_data['close']
        
        # Create line chart
        fig = self.plotly_charts.create_simple_line_chart(
            data=sample_series,
            title="Test Line Chart"
        )
        
        # Check that axis titles are empty
        assert fig.layout.xaxis.title.text == "", "Line chart X-axis title should be empty"
        assert fig.layout.yaxis.title.text == "", "Line chart Y-axis title should be empty"
    
    def test_chart_with_indicators(self):
        """Test chart with indicators maintains clean layout."""
        # Create mock indicator result
        indicator_result = IndicatorResult(
            name="Test MA",
            data={'ma': self.sample_data['close'].rolling(20).mean()},
            metadata={'category': 'trend'}
        )
        
        indicators = {'test_ma': indicator_result}
        
        # Create chart with indicators
        fig = self.plotly_charts.create_candlestick_chart(
            data=self.sample_data,
            indicators=indicators,
            title="Test Chart with Indicators"
        )
        
        # Check that axis titles are still empty
        assert fig.layout.xaxis.title.text == "", "X-axis title should be empty with indicators"
        assert fig.layout.yaxis.title.text == "", "Y-axis title should be empty with indicators"
        
        # Check that indicator trace exists
        ma_traces = [trace for trace in fig.data if 'ma' in trace.name.lower()]
        assert len(ma_traces) > 0, "Chart should have moving average trace"


if __name__ == "__main__":
    # Run tests
    test_instance = TestChartLayoutImprovements()
    test_instance.setup_method()
    
    print("Running chart layout tests...")
    
    try:
        test_instance.test_axis_labels_removed()
        print("✅ Axis labels removal test passed")
        
        test_instance.test_subplot_titles_removed()
        print("✅ Subplot titles removal test passed")
        
        test_instance.test_chart_functionality_preserved()
        print("✅ Chart functionality preservation test passed")
        
        test_instance.test_simple_line_chart_labels_removed()
        print("✅ Simple line chart labels removal test passed")
        
        test_instance.test_chart_with_indicators()
        print("✅ Chart with indicators test passed")
        
        print("\n🎉 All chart layout tests passed!")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        raise
