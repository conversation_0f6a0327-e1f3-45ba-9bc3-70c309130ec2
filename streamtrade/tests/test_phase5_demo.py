"""
Phase 5.1 Implementation Demo and Test
Demonstrates the new timezone-aware timeframe system and N Days Back loading.
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

import pandas as pd
import numpy as np
from datetime import datetime, timedelta, time

from streamtrade.config.minimal_settings import MinimalSettings
from streamtrade.data.minimal_session_converter import MinimalSessionConverter


def create_sample_m1_data(start_date: datetime, days: int = 5) -> pd.DataFrame:
    """Create sample M1 data for testing."""
    print(f"Creating {days} days of sample M1 data starting from {start_date}")
    
    # Create timestamps for multiple days
    all_times = []
    current_date = start_date.date()
    
    for day in range(days):
        # Each day starts at 16:00 (forex market open)
        day_start = datetime.combine(current_date, time(16, 0))
        
        # Create 20 hours of data (16:00 to 12:00 next day, with gaps)
        for hour in range(20):
            hour_start = day_start + timedelta(hours=hour)
            
            # Skip weekend gaps (simplified)
            if hour_start.weekday() >= 5:  # Saturday, Sunday
                continue
            
            # Create M1 data for this hour
            for minute in range(60):
                timestamp = hour_start + timedelta(minutes=minute)
                all_times.append(timestamp)
        
        current_date += timedelta(days=1)
    
    # Create OHLCV data
    base_price = 1.0800
    data = []
    
    for i, timestamp in enumerate(all_times):
        # Simulate price movement
        price_change = np.random.uniform(-0.0010, 0.0010)
        open_price = base_price + price_change
        
        high_offset = np.random.uniform(0, 0.0005)
        low_offset = np.random.uniform(0, 0.0005)
        close_change = np.random.uniform(-0.0005, 0.0005)
        
        high_price = open_price + high_offset
        low_price = open_price - low_offset
        close_price = open_price + close_change
        
        # Ensure OHLC logic is correct
        high_price = max(high_price, open_price, close_price)
        low_price = min(low_price, open_price, close_price)
        
        data.append({
            'timestamp': timestamp,
            'open': round(open_price, 5),
            'high': round(high_price, 5),
            'low': round(low_price, 5),
            'close': round(close_price, 5),
            'volume': np.random.randint(50, 200)
        })
        
        # Update base price for next candle
        base_price = close_price
    
    df = pd.DataFrame(data)
    df.set_index('timestamp', inplace=True)
    df.sort_index(inplace=True)
    
    print(f"✓ Created {len(df)} M1 candles from {df.index[0]} to {df.index[-1]}")
    return df


def test_timezone_settings():
    """Test timezone and market session settings."""
    print("\n=== Testing Timezone & Market Session Settings ===")
    
    settings = MinimalSettings()
    
    # Test timezone settings
    data_tz = settings.get('timezone.data_timezone')
    display_tz = settings.get('timezone.display_timezone')
    print(f"✓ Data timezone: {data_tz}")
    print(f"✓ Display timezone: {display_tz}")
    
    # Test market session settings
    forex_open = settings.get('market_sessions.forex_open')
    non_forex_open = settings.get('market_sessions.non_forex_open')
    print(f"✓ Forex market open: {forex_open}")
    print(f"✓ Non-forex market open: {non_forex_open}")
    
    # Test pair classification
    test_pairs = ['EURUSD', 'GBPJPY', 'XAUUSD', 'SPXUSD', 'NSXUSD']
    for pair in test_pairs:
        is_forex = settings.is_forex_pair(pair)
        market_open = settings.get_market_open_time(pair)
        print(f"✓ {pair}: {'Forex' if is_forex else 'Non-Forex'}, opens at {market_open}")


def test_session_aware_conversion():
    """Test session-aware timeframe conversion."""
    print("\n=== Testing Session-Aware Timeframe Conversion ===")
    
    converter = MinimalSessionConverter()
    
    # Test session boundaries
    test_date = datetime(2024, 1, 15)  # Monday
    
    # Forex sessions
    forex_sessions = converter.get_session_boundaries('EURUSD', test_date)
    print(f"✓ EURUSD sessions on {test_date.date()}:")
    for i, session in enumerate(forex_sessions['h4_sessions']):
        print(f"  H4 Session {i+1}: {session['start'].strftime('%H:%M')} - {session['end'].strftime('%H:%M')}")
    
    # Non-forex sessions
    gold_sessions = converter.get_session_boundaries('XAUUSD', test_date)
    print(f"✓ XAUUSD sessions on {test_date.date()}:")
    for i, session in enumerate(gold_sessions['h4_sessions']):
        print(f"  H4 Session {i+1}: {session['start'].strftime('%H:%M')} - {session['end'].strftime('%H:%M')}")


def test_n_days_back_loading():
    """Test N Days Back loading strategy."""
    print("\n=== Testing N Days Back Loading Strategy ===")
    
    settings = MinimalSettings()
    
    # Test settings
    days_back_default = settings.get('data_loading.days_back_default')
    max_candles_load = settings.get('data_loading.max_candles_load')
    max_candles_display = settings.get('data_loading.max_candles_display')
    
    print(f"✓ Default days back: {days_back_default}")
    print(f"✓ Max candles to load: {max_candles_load:,}")
    print(f"✓ Max candles to display: {max_candles_display:,}")
    
    # Simulate N Days Back loading
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days_back_default)
    
    print(f"✓ Loading range: {start_date.date()} to {end_date.date()}")
    print(f"✓ This replaces the old 'Last N Candles' approach with time-based loading")


def test_timeframe_conversion_accuracy():
    """Test the accuracy of session-aware timeframe conversion."""
    print("\n=== Testing Timeframe Conversion Accuracy ===")
    
    converter = MinimalSessionConverter()
    
    # Create 2 days of M1 data starting Monday 16:00
    start_time = datetime(2024, 1, 15, 16, 0)  # Monday 16:00
    m1_data = create_sample_m1_data(start_time, days=2)
    
    print(f"✓ Sample M1 data: {len(m1_data)} candles")
    print(f"  Time range: {m1_data.index[0]} to {m1_data.index[-1]}")
    
    # Convert to H4 using session boundaries
    h4_data = converter.convert_to_h4(m1_data, 'EURUSD')
    
    print(f"✓ Converted to H4: {len(h4_data)} candles")
    
    if not h4_data.empty:
        print("  H4 Candles:")
        for i, (timestamp, candle) in enumerate(h4_data.iterrows()):
            print(f"    {i+1}. {timestamp.strftime('%Y-%m-%d %H:%M')} | "
                  f"O={candle['open']:.5f} H={candle['high']:.5f} "
                  f"L={candle['low']:.5f} C={candle['close']:.5f}")
    
    # Verify session boundaries
    print("\n✓ Session Boundary Verification:")
    for i, (timestamp, candle) in enumerate(h4_data.iterrows()):
        session_info = converter.get_session_boundaries('EURUSD', timestamp)
        expected_session = session_info['h4_sessions'][timestamp.hour // 4 if timestamp.hour >= 16 else (timestamp.hour + 24) // 4 - 1]
        print(f"  H4 {i+1}: Expected session {expected_session['start'].strftime('%H:%M')}-{expected_session['end'].strftime('%H:%M')}")


def test_forex_vs_non_forex_differences():
    """Test differences between Forex and Non-Forex session handling."""
    print("\n=== Testing Forex vs Non-Forex Session Differences ===")
    
    converter = MinimalSessionConverter()
    test_date = datetime(2024, 1, 15)  # Monday
    
    # Compare session boundaries
    forex_sessions = converter.get_session_boundaries('EURUSD', test_date)
    gold_sessions = converter.get_session_boundaries('XAUUSD', test_date)
    
    print("✓ Session Comparison:")
    print(f"  EURUSD (Forex) starts at: {forex_sessions['session_start'].strftime('%H:%M')}")
    print(f"  XAUUSD (Gold) starts at: {gold_sessions['session_start'].strftime('%H:%M')}")
    
    print("\n  First H4 Session Comparison:")
    forex_first = forex_sessions['h4_sessions'][0]
    gold_first = gold_sessions['h4_sessions'][0]
    
    print(f"  EURUSD H4-1: {forex_first['start'].strftime('%H:%M')} - {forex_first['end'].strftime('%H:%M')}")
    print(f"  XAUUSD H4-1: {gold_first['start'].strftime('%H:%M')} - {gold_first['end'].strftime('%H:%M')}")
    
    # This demonstrates the 1-hour offset between Forex and Non-Forex markets
    time_diff = gold_first['start'] - forex_first['start']
    print(f"✓ Time difference: {time_diff.total_seconds() / 3600} hours")


def main():
    """Run all Phase 5.1 tests."""
    print("🚀 Phase 5.1 Implementation Demo")
    print("=" * 50)
    
    try:
        test_timezone_settings()
        test_session_aware_conversion()
        test_n_days_back_loading()
        test_timeframe_conversion_accuracy()
        test_forex_vs_non_forex_differences()
        
        print("\n" + "=" * 50)
        print("✅ All Phase 5.1 tests completed successfully!")
        print("\n📋 Implementation Summary:")
        print("  ✓ User Settings System with project-local storage")
        print("  ✓ Timezone & Market Session Configuration")
        print("  ✓ Session-Aware Timeframe Conversion")
        print("  ✓ N Days Back Loading Strategy")
        print("  ✓ Forex vs Non-Forex market differentiation")
        print("  ✓ Gap-aware session boundary handling")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
