"""
Unit tests for indicators module.
"""

import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from streamtrade.config.logging_config import setup_logging
from streamtrade.indicators.technical_indicators import TechnicalIndicators, SMA, EMA, RSI, MACD
from streamtrade.indicators.indicator_manager import IndicatorManager
from streamtrade.indicators.base_indicator import BaseIndicator

# Setup logging for tests
setup_logging(level="WARNING", console_output=False)


class TestTechnicalIndicators(unittest.TestCase):
    """Test technical indicators functionality."""
    
    def setUp(self):
        # Create sample OHLCV data
        dates = pd.date_range(start='2024-01-01', periods=100, freq='1h')
        np.random.seed(42)  # For reproducible results
        
        # Generate realistic price data
        base_price = 1.1000
        price_changes = np.random.normal(0, 0.001, len(dates))
        prices = base_price + np.cumsum(price_changes)
        
        # Create OHLCV data
        self.sample_data = pd.DataFrame({
            'open': prices + np.random.normal(0, 0.0005, len(dates)),
            'high': prices + np.abs(np.random.normal(0, 0.001, len(dates))),
            'low': prices - np.abs(np.random.normal(0, 0.001, len(dates))),
            'close': prices,
            'volume': np.random.randint(1000, 10000, len(dates))
        }, index=dates)
        
        # Ensure OHLC relationships are correct
        for i in range(len(self.sample_data)):
            row = self.sample_data.iloc[i]
            high = max(row['open'], row['close']) + abs(np.random.normal(0, 0.0005))
            low = min(row['open'], row['close']) - abs(np.random.normal(0, 0.0005))
            self.sample_data.iloc[i, self.sample_data.columns.get_loc('high')] = high
            self.sample_data.iloc[i, self.sample_data.columns.get_loc('low')] = low
    
    def test_sma_indicator(self):
        """Test SMA indicator calculation."""
        sma = SMA()
        result = sma.calculate(self.sample_data, period=20)
        
        self.assertTrue(result.metadata['calculation_success'])
        self.assertIn('sma', result.data)
        self.assertEqual(len(result.data['sma']), len(self.sample_data))
        self.assertFalse(result.data['sma'].isna().all())
    
    def test_ema_indicator(self):
        """Test EMA indicator calculation."""
        ema = EMA()
        result = ema.calculate(self.sample_data, period=20)
        
        self.assertTrue(result.metadata['calculation_success'])
        self.assertIn('ema', result.data)
        self.assertEqual(len(result.data['ema']), len(self.sample_data))
    
    def test_rsi_indicator(self):
        """Test RSI indicator calculation."""
        rsi = RSI()
        result = rsi.calculate(self.sample_data, period=14)
        
        self.assertTrue(result.metadata['calculation_success'])
        self.assertIn('rsi', result.data)
        
        # RSI should be between 0 and 100
        rsi_values = result.data['rsi'].dropna()
        self.assertTrue((rsi_values >= 0).all())
        self.assertTrue((rsi_values <= 100).all())
    
    def test_macd_indicator(self):
        """Test MACD indicator calculation."""
        macd = MACD()
        result = macd.calculate(self.sample_data, fast=12, slow=26, signal=9)
        
        self.assertTrue(result.metadata['calculation_success'])
        self.assertIn('macd', result.data)
        self.assertIn('signal', result.data)
        self.assertIn('histogram', result.data)
    
    def test_parameter_validation(self):
        """Test parameter validation."""
        sma = SMA()
        
        # Valid parameters
        result = sma.calculate(self.sample_data, period=20)
        self.assertTrue(result.metadata['calculation_success'])
        
        # Invalid parameters should fail gracefully
        result = sma.calculate(self.sample_data, period=-5)
        self.assertFalse(result.metadata['calculation_success'])
    
    def test_technical_indicators_factory(self):
        """Test TechnicalIndicators factory class."""
        # Test getting available indicators
        available = TechnicalIndicators.get_available_indicators()
        self.assertIsInstance(available, dict)
        self.assertIn('SMA', available)
        self.assertIn('RSI', available)
        
        # Test getting indicator by name
        sma = TechnicalIndicators.get_indicator('SMA')
        self.assertIsInstance(sma, BaseIndicator)
        
        # Test invalid indicator name
        with self.assertRaises(ValueError):
            TechnicalIndicators.get_indicator('INVALID')
        
        # Test indicators by category
        by_category = TechnicalIndicators.get_indicators_by_category()
        self.assertIsInstance(by_category, dict)
        self.assertIn('trend', by_category)
        self.assertIn('momentum', by_category)


class TestIndicatorManager(unittest.TestCase):
    """Test IndicatorManager functionality."""
    
    def setUp(self):
        self.manager = IndicatorManager()
        
        # Create sample data
        dates = pd.date_range(start='2024-01-01', periods=50, freq='1h')
        self.sample_data = pd.DataFrame({
            'open': np.random.uniform(1.0800, 1.0900, len(dates)),
            'high': np.random.uniform(1.0850, 1.0950, len(dates)),
            'low': np.random.uniform(1.0750, 1.0850, len(dates)),
            'close': np.random.uniform(1.0800, 1.0900, len(dates)),
            'volume': np.random.randint(100, 1000, len(dates))
        }, index=dates)
    
    def test_add_indicator(self):
        """Test adding indicators."""
        # Add valid indicator
        success = self.manager.add_indicator(
            name="sma_20",
            indicator_type="SMA",
            parameters={"period": 20}
        )
        self.assertTrue(success)
        self.assertIn("sma_20", self.manager.indicators)
        
        # Try to add duplicate name
        success = self.manager.add_indicator(
            name="sma_20",
            indicator_type="SMA",
            parameters={"period": 30}
        )
        self.assertFalse(success)
        
        # Add invalid indicator type
        success = self.manager.add_indicator(
            name="invalid",
            indicator_type="INVALID_INDICATOR"
        )
        self.assertFalse(success)
    
    def test_remove_indicator(self):
        """Test removing indicators."""
        # Add indicator first
        self.manager.add_indicator("test_sma", "SMA", {"period": 20})
        
        # Remove existing indicator
        success = self.manager.remove_indicator("test_sma")
        self.assertTrue(success)
        self.assertNotIn("test_sma", self.manager.indicators)
        
        # Try to remove non-existent indicator
        success = self.manager.remove_indicator("non_existent")
        self.assertFalse(success)
    
    def test_calculate_all(self):
        """Test calculating all indicators."""
        # Add multiple indicators
        self.manager.add_indicator("sma_20", "SMA", {"period": 20})
        self.manager.add_indicator("rsi_14", "RSI", {"period": 14})
        
        # Calculate all
        results = self.manager.calculate_all(self.sample_data)
        
        self.assertIsInstance(results, dict)
        self.assertIn("sma_20", results)
        self.assertIn("rsi_14", results)
    
    def test_enable_disable_indicator(self):
        """Test enabling/disabling indicators."""
        self.manager.add_indicator("test_sma", "SMA", {"period": 20})
        
        # Disable indicator
        success = self.manager.disable_indicator("test_sma")
        self.assertTrue(success)
        self.assertFalse(self.manager.indicators["test_sma"].enabled)
        
        # Enable indicator
        success = self.manager.enable_indicator("test_sma")
        self.assertTrue(success)
        self.assertTrue(self.manager.indicators["test_sma"].enabled)
    
    def test_export_import_configuration(self):
        """Test configuration export/import."""
        # Add some indicators
        self.manager.add_indicator("sma_20", "SMA", {"period": 20})
        self.manager.add_indicator("rsi_14", "RSI", {"period": 14})
        
        # Export configuration
        config_json = self.manager.export_configuration()
        self.assertIsInstance(config_json, str)
        
        # Clear and import
        self.manager.clear_all()
        self.assertEqual(len(self.manager.indicators), 0)
        
        success = self.manager.import_configuration(config_json)
        self.assertTrue(success)
        self.assertEqual(len(self.manager.indicators), 2)
        self.assertIn("sma_20", self.manager.indicators)
        self.assertIn("rsi_14", self.manager.indicators)
    
    def test_get_summary(self):
        """Test getting manager summary."""
        # Add indicators
        self.manager.add_indicator("sma_20", "SMA", {"period": 20})
        self.manager.add_indicator("rsi_14", "RSI", {"period": 14})
        self.manager.disable_indicator("rsi_14")
        
        summary = self.manager.get_summary()
        
        self.assertEqual(summary['total_indicators'], 2)
        self.assertEqual(summary['enabled_indicators'], 1)
        self.assertEqual(summary['disabled_indicators'], 1)
        self.assertIn('trend', summary['categories'])
        self.assertIn('momentum', summary['categories'])


def run_tests():
    """Run all indicator tests."""
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestTechnicalIndicators,
        TestIndicatorManager
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
