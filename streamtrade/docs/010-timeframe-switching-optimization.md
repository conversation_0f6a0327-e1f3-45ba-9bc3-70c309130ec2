# Timeframe Switching Optimization

**Date**: 2025-06-27  
**Status**: ✅ Completed  
**Files Modified**: 
- `streamtrade/data/data_manager.py`
- `streamtrade/visualization/chart_viewer.py`
- `streamtrade/tests/test_timeframe_switching.py` (new)

## Problem Statement

Sebelum optimisasi ini, sistem timeframe switching memiliki beberapa masalah:

1. **Tidak menggunakan cache M1 yang efisien**: Saat switch timeframe, sistem selalu load data baru dari file meskipun sudah ada cache M1
2. **Tidak preserve user request**: Saat user load 12 candles H1 kemudian switch ke timeframe lain, sistem tidak mengingat bahwa user hanya ingin 12 candles
3. **Logika caching yang terlalu ketat**: Sistem menolak menggunakan cache M1 jika data tidak cukup untuk menghasilkan jumlah candles yang diminta, padahal seharusnya menampilkan data yang tersedia
4. **Inefficient data loading**: Setiap timeframe switch memuat ulang semua data (max 10000 candles) dari file

## Solution Overview

### 1. User Context Preservation
- **Added `_user_context`**: Dictionary untuk menyimpan konteks user request per pair
- **Structure**: `{pair: {'requested_candles': int, 'last_timeframe': str}}`
- **Purpose**: Mengingat berapa candles yang diminta user untuk digunakan saat timeframe switching

### 2. Optimized M1 Cache Logic
- **Modified `_can_use_cached_m1()`**: Lebih permisif dalam menggunakan cache M1
- **Graceful degradation**: Jika data tidak cukup, tampilkan yang tersedia (tidak reload)
- **Better logging**: Informasi yang lebih jelas tentang berapa candles tersedia vs diminta

### 3. New Timeframe Switching Method
- **Added `get_data_for_timeframe_switch()`**: Method khusus untuk timeframe switching
- **Preserves user context**: Menggunakan jumlah candles yang diminta user sebelumnya
- **Cache-first approach**: Prioritas menggunakan cache M1 sebelum load dari file

### 4. ChartViewer Integration
- **Modified `change_timeframe()`**: Menggunakan method baru untuk timeframe switching
- **Added `get_data_info()`**: Method untuk mendapatkan informasi data dan user context
- **Better error handling**: Handling yang lebih baik untuk kasus data tidak tersedia

## Technical Implementation

### DataManager Changes

#### 1. User Context Storage
```python
# New attribute in __init__
self._user_context = {}  # {pair: {'requested_candles': int, 'last_timeframe': str}}

# Store context when loading data
if preserve_user_request and max_candles is not None:
    self._user_context[pair] = {
        'requested_candles': max_candles,
        'last_timeframe': timeframe
    }
```

#### 2. Optimized Cache Logic
```python
def _can_use_cached_m1(self, pair, timeframe, max_candles, start_date, end_date):
    # More permissive - always try to use cache if available
    # Handle insufficient data gracefully in conversion
    return len(cached_data) > 0 and basic_checks_pass
```

#### 3. New Timeframe Switching Method
```python
def get_data_for_timeframe_switch(self, pair, new_timeframe, use_cache=True):
    # Get user context to preserve candle count
    user_context = self.get_user_context(pair)
    requested_candles = user_context['requested_candles'] if user_context else default
    
    # Try cached M1 first, fallback to fresh load if needed
    if self._can_use_cached_m1(...):
        return self._convert_from_cached_m1(...)
    else:
        return self.get_data(...)
```

### ChartViewer Changes

#### 1. Optimized Timeframe Switching
```python
def change_timeframe(self, new_timeframe):
    # Use new optimized method
    data = data_manager.get_data_for_timeframe_switch(
        pair=self.current_pair,
        new_timeframe=new_timeframe
    )
    # Update state and re-apply indicators
```

#### 2. Data Information Method
```python
def get_data_info(self):
    return {
        'pair': self.current_pair,
        'timeframe': self.current_timeframe,
        'candles_loaded': len(self.current_data),
        'user_context': data_manager.get_user_context(self.current_pair)
    }
```

## Behavior Examples

### Example 1: Sufficient Data Scenario
```
1. User loads 12 candles H1 → System stores context: {requested_candles: 12, last_timeframe: 'H1'}
2. User switches to H4 → System uses cached M1 data, converts to H4, returns 3 candles (12H ÷ 4H = 3)
3. User switches to M30 → System uses cached M1 data, converts to M30, returns 12 candles (if enough data)
```

### Example 2: Insufficient Data Scenario
```
1. User loads 12 candles H1 → System stores context: {requested_candles: 12, last_timeframe: 'H1'}
2. User switches to D1 → System checks cached M1 data, finds only enough for 1 D1 candle
3. System returns 1 D1 candle (not empty result), logs: "1/12 D1 candles (limited by available data)"
```

### Example 3: No Cache Scenario
```
1. User loads 12 candles H1 → Cache M1 data insufficient for other timeframes
2. User switches to D1 → System loads fresh M1 data from files with appropriate buffer
3. System converts to D1, returns up to 12 candles, updates M1 cache for future switches
```

## Benefits

### 1. Performance Improvements
- **Faster timeframe switching**: Menggunakan cache M1 yang sudah ada
- **Reduced file I/O**: Tidak perlu reload data dari file untuk setiap switch
- **Memory efficiency**: Reuse data yang sudah di-load sebelumnya

### 2. Better User Experience
- **Consistent candle count**: User request untuk jumlah candles dipertahankan
- **Graceful degradation**: Tampilkan data yang tersedia, tidak error atau kosong
- **Clear feedback**: Log yang informatif tentang berapa data yang tersedia

### 3. Logical Behavior
- **Predictable results**: User tahu apa yang diharapkan saat switch timeframe
- **No look-ahead bias**: Tetap menggunakan data terbaru (tail), tidak mengambil data masa depan
- **Proper data handling**: Handle edge cases dengan baik

## Testing

### Test Coverage
- ✅ User context preservation across timeframe switches
- ✅ Candle count preservation logic
- ✅ Insufficient data handling (graceful degradation)
- ✅ ChartViewer integration
- ✅ M1 cache utilization
- ✅ Cache statistics and cleanup

### Test Execution
```bash
# Activate environment
source /home/<USER>/Learn/python/notebooks/venv/bin/activate

# Run timeframe switching tests
cd streamtrade
python tests/test_timeframe_switching.py
```

### Test Results
All tests passed successfully:
```
✅ User context preservation test passed
✅ Candle count preservation test passed
✅ Insufficient data handling test passed
✅ ChartViewer integration test passed
✅ M1 cache utilization test passed
✅ Cache stats and cleanup test passed

🎉 All timeframe switching tests passed!
```

### Demo Execution
```bash
# Run logic demonstration
cd streamtrade
python demo_simple.py
```

Demo menunjukkan:
- User loads 12 H1 candles → Context tersimpan
- Switch ke H4 → Dapat 6/12 candles (limited by data)
- Switch ke M30 → Dapat 12/12 candles (sufficient data)
- Switch ke D1 → Dapat 1/12 candles (insufficient data)
- Semua menggunakan cached M1 data, tidak reload dari file

## Configuration

### Settings Impact
- `max_candles_display`: Digunakan sebagai fallback jika tidak ada user context
- `cache_timeout_minutes`: Menentukan berapa lama M1 cache valid
- `max_memory_mb`: Mempengaruhi berapa banyak data yang bisa di-cache

### No Breaking Changes
- Semua existing functionality tetap bekerja
- Backward compatibility terjaga
- Optional parameters dengan default values

## Future Enhancements

### Potential Improvements
1. **Smart cache warming**: Pre-load M1 data untuk timeframes yang sering digunakan
2. **User preference storage**: Simpan preferensi user untuk jumlah candles per timeframe
3. **Cache persistence**: Simpan cache ke disk untuk session berikutnya
4. **Progressive loading**: Load data secara bertahap untuk timeframes besar

### Monitoring Points
1. **Cache hit rate**: Monitor seberapa sering cache M1 digunakan
2. **Memory usage**: Pastikan cache tidak menggunakan terlalu banyak memory
3. **Performance metrics**: Track waktu yang dibutuhkan untuk timeframe switching

## Conclusion

Optimisasi ini secara signifikan meningkatkan performa dan user experience untuk timeframe switching di platform Lionaire. Sistem sekarang lebih efisien, predictable, dan user-friendly dalam menangani perubahan timeframe sambil mempertahankan integritas data dan menghindari look-ahead bias.
