# Settings Panel User Guide

**Date**: 2025-06-28  
**Status**: ✅ AVAILABLE  
**Feature**: Phase 5.1 Settings Interface  

## 🎯 Overview

The Settings Panel provides a user-friendly interface to configure Phase 5.1 features including timezone settings, market sessions, data loading preferences, and cache management.

## 🚀 How to Access Settings Panel

### Step 1: Open Lionaire Platform
1. Navigate to your browser
2. Go to `http://localhost:8501` (or your Streamlit URL)
3. Wait for the application to load

### Step 2: Access Settings
1. Look at the **left sidebar** (Control Panel)
2. Find the **"⚙️ Platform Settings"** section
3. Click **"🚀 Phase 5.1 Settings"** button
4. The settings panel will open in the main content area

### Step 3: Navigate Settings
The settings panel has **4 main tabs**:

#### 🌍 **Timezone & Sessions**
- **Data Timezone**: Configure source data timezone (default: UTC-5)
- **Display Timezone**: Set interface display timezone (default: UTC+7)
- **Market Sessions**: Set Forex (16:00) and Non-Forex (17:00) open times
- **Non-Forex Symbols**: Select instruments using Non-Forex sessions
- **Session Preview**: See H4 session boundaries for both market types

#### 📊 **Data Loading**
- **Days Back**: Set default loading period (default: 5 days)
- **Max Candles to Load**: File loading limit (default: 200,000)
- **Max Candles to Display**: Chart display limit (default: 15,000)
- **Enabled Timeframes**: Select available timeframes
- **Cache All TF**: Option to pre-cache all timeframes
- **Insufficient Data**: Choose behavior for missing data

#### 💾 **Cache Settings**
- **Cache Size**: Set maximum cache size in GB (default: 10GB)
- **Compression**: Choose cache compression method
- **Disk Cache**: Enable/disable persistent caching
- **Cache Statistics**: View current cache usage
- **Clear Cache**: Button to clear all cached data

#### 🎨 **UI Preferences**
- **Default Timeframe**: Set startup timeframe (default: H1)
- **Chart Style**: Choose candlestick, OHLC, or line
- **Weekend Gaps**: Option to remove gaps in charts
- **Crosshair Settings**: Configure vertical/horizontal crosshairs

## 📋 Phase 5.1 Status Display

At the bottom of the settings panel, you'll see:

### ✅ **Implementation Status**
- User Settings System ✅
- Timezone Configuration ✅
- Market Sessions ✅
- Session-Aware Conversion ✅
- N Days Back Loading ✅
- Enhanced Data Manager ✅
- Settings UI ✅

### 🎯 **Next Steps**
- **Phase 5.2**: Smart Disk Cache System
- **Phase 5.3**: Advanced Indicator Cache

## 🔧 Key Features

### **Session-Aware Timeframe Conversion**
The settings panel shows **real-time session boundary previews**:

**Forex (EURUSD) - 16:00 start:**
- H4-1: 16:00 → 20:00
- H4-2: 20:00 → 00:00
- H4-3: 00:00 → 04:00
- H4-4: 04:00 → 08:00
- H4-5: 08:00 → 12:00
- H4-6: 12:00 → Close

**Non-Forex (XAUUSD) - 17:00 start:**
- H4-1: 17:00 → 21:00
- H4-2: 21:00 → 01:00
- H4-3: 01:00 → 05:00
- H4-4: 05:00 → 09:00
- H4-5: 09:00 → 13:00
- H4-6: 13:00 → Close

### **N Days Back Loading**
Replace the old "Last N Candles" approach with intuitive time-based loading:
- **Old**: "Load 1000 candles" (unpredictable time range)
- **New**: "Load 5 days back" (predictable time range)

### **Cache Management**
Monitor and control cache usage:
- View cache entries and size
- Monitor cache utilization percentage
- Clear cache when needed
- Configure maximum cache size

## 🔄 How to Return to Chart

1. Click the **"⬅️ Back to Chart"** button at the top-right of the settings panel
2. You'll return to the main chart view
3. Settings are automatically saved (when save buttons are clicked)

## 💾 Saving Settings

Each settings tab has a **"💾 Save [Category] Settings"** button:
- Click to save changes in that category
- Settings are stored in project-local JSON files
- Settings persist across browser sessions
- No database required - all stored locally

## 🔍 Troubleshooting

### **Settings Panel Not Loading**
- Check browser console for errors
- Refresh the page (Ctrl+F5)
- Ensure Phase 5.1 components are properly installed

### **Settings Not Saving**
- Check file permissions in project directory
- Ensure `./config/` directory exists and is writable
- Look for error messages in the interface

### **Cache Statistics Not Showing**
- This is normal if no data has been loaded yet
- Load some chart data first to see cache statistics
- Cache stats appear after first data loading

## 📖 Related Documentation

- **Implementation Details**: `/streamtrade/docs/012-phase5-1-implementation.md`
- **Completion Summary**: `/streamtrade/docs/013-phase5-1-completion-summary.md`
- **Project Plans**: `/streamtrade/plans.md`

## 🎉 Conclusion

The Settings Panel provides complete control over Phase 5.1 features:
- ✅ **Easy Access**: One-click access from sidebar
- ✅ **Organized Interface**: Tabbed layout for different categories
- ✅ **Real-time Preview**: See session boundaries as you configure
- ✅ **Persistent Storage**: Settings saved locally in JSON format
- ✅ **Status Monitoring**: Track implementation progress
- ✅ **Cache Management**: Monitor and control cache usage

**The Phase 5.1 timezone-aware timeframe system is now fully accessible through the user interface!**

---

*Settings Panel implemented as part of Phase 5.1 completion on 2025-06-28*
