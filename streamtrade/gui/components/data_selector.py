"""
Data selector component for Lionaire GUI.
"""

import streamlit as st
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Tuple

from ...config.logging_config import get_logger
from ...config.strings import get_string, strings
from ...config.user_settings import get_user_settings
from ...visualization.chart_viewer import ChartViewer

logger = get_logger(__name__)


class DataSelector:
    """
    Data selector component for choosing pairs, timeframes, and date ranges.
    """
    
    def __init__(self, chart_viewer: ChartViewer):
        self.chart_viewer = chart_viewer
        self.user_settings = get_user_settings()
        
    def render(self) -> Dict[str, Any]:
        """
        Render data selector UI and return selected parameters.
        
        Returns:
            Dictionary with selected data parameters
        """
        st.subheader(get_string("DATA_SELECTION", "title"))

        # Check if user settings have been updated and reload if needed
        if 'user_settings_reload_trigger' in st.session_state:
            # Force reload of user settings in chart_viewer
            self.chart_viewer.data_manager.user_settings.reload_settings()

        # Get available options (already filtered by user settings in chart_viewer)
        available_pairs = self.chart_viewer.get_available_pairs()
        available_timeframes = self.chart_viewer.get_available_timeframes()

        if not available_pairs:
            st.error(get_string("MESSAGES", "no_pairs_available"))
            return {}

        if not available_timeframes:
            st.error("No enabled timeframes available. Please check your settings.")
            return {}
        
        # Create columns for layout
        col1, col2 = st.columns(2)
        
        with col1:
            # Currency pair selection with persistence
            default_pair_index = 0
            if st.session_state.last_selected_pair and st.session_state.last_selected_pair in available_pairs:
                default_pair_index = available_pairs.index(st.session_state.last_selected_pair)

            selected_pair = st.selectbox(
                get_string("DATA_SELECTION", "currency_pair"),
                options=available_pairs,
                index=default_pair_index,
                help=get_string("DATA_SELECTION", "currency_pair_help")
            )

            # Update session state
            st.session_state.last_selected_pair = selected_pair

        with col2:
            # Timeframe selection with persistence and user default
            default_timeframe = self.user_settings.get('ui_preferences.default_timeframe', 'H1')
            default_index = 0

            # Use last selected timeframe if available, otherwise use user default
            if st.session_state.last_selected_timeframe and st.session_state.last_selected_timeframe in available_timeframes:
                default_index = available_timeframes.index(st.session_state.last_selected_timeframe)
            elif default_timeframe in available_timeframes:
                default_index = available_timeframes.index(default_timeframe)

            selected_timeframe = st.selectbox(
                get_string("DATA_SELECTION", "timeframe"),
                options=available_timeframes,
                index=default_index,
                help=get_string("DATA_SELECTION", "timeframe_help")
            )

            # Update session state
            st.session_state.last_selected_timeframe = selected_timeframe

        # Data loading strategy selection
        st.subheader(get_string("DATA_SELECTION", "data_loading_strategy"))

        date_option = st.radio(
            get_string("DATA_SELECTION", "data_loading_option"),
            options=[
                get_string("DATA_SELECTION", "n_days_back"),
                get_string("DATA_SELECTION", "date_range_custom"),
                get_string("DATA_SELECTION", "all_available")
            ],
            index=0,
            horizontal=True,
            help="Select how you want to load historical data"
        )

        start_date = None
        end_date = None
        days_back = None

        if date_option == get_string("DATA_SELECTION", "n_days_back"):
            # Get user default
            default_days = self.user_settings.get('data_loading.days_back_default', 5)
            min_days = self.user_settings.get('data_loading.days_back_minimum', 1)

            days_back = st.number_input(
                get_string("DATA_SELECTION", "number_of_days"),
                min_value=min_days,
                max_value=None,  # Remove arbitrary 30-day limit
                value=default_days,
                step=1,
                help=get_string("DATA_SELECTION", "number_of_days_help") + f" (default: {default_days} days, limited by max_candles_load setting)"
            )
        
        elif date_option == get_string("DATA_SELECTION", "date_range_custom"):
            col1, col2 = st.columns(2)
            
            with col1:
                start_date = st.date_input(
                    get_string("DATA_SELECTION", "start_date"),
                    value=datetime.now() - timedelta(days=30),
                    help="Start date for data range"
                )

            with col2:
                end_date = st.date_input(
                    get_string("DATA_SELECTION", "end_date"),
                    value=datetime.now(),
                    help="End date for data range"
                )
            
            # Convert to datetime
            if start_date and end_date:
                start_date = datetime.combine(start_date, datetime.min.time())
                end_date = datetime.combine(end_date, datetime.max.time())
                
                if start_date >= end_date:
                    st.error("Start date must be before end date")
                    return {}

        else:  # All Available
            st.info("📊 This will load all available historical data for the selected pair and timeframe.")
            max_candles = self.user_settings.get('data_loading.max_candles_load', 200000)
            st.caption(f"Limited by max_candles_load setting: {max_candles:,} candles")
        
        # Load data button
        if st.button(get_string("DATA_SELECTION", "load_data"), type="primary", use_container_width=True):
            with st.spinner("Loading data..."):
                # Use the new N Days Back system
                if date_option == get_string("DATA_SELECTION", "n_days_back"):
                    success = self.chart_viewer.load_data_n_days_back(
                        pair=selected_pair,
                        timeframe=selected_timeframe,
                        days_back=days_back
                    )
                elif date_option == get_string("DATA_SELECTION", "date_range_custom"):
                    success = self.chart_viewer.load_data_range(
                        pair=selected_pair,
                        timeframe=selected_timeframe,
                        start_date=start_date,
                        end_date=end_date
                    )
                else:  # All Available
                    success = self.chart_viewer.load_all_available_data(
                        pair=selected_pair,
                        timeframe=selected_timeframe
                    )
                
                if success:
                    st.success(f"✅ Loaded data for {selected_pair} {selected_timeframe}")

                    # Create new chart with loaded data
                    with st.spinner("Creating chart..."):
                        fig = self.chart_viewer.create_chart()
                        if fig:
                            st.session_state.chart_figure = fig

                    # Show data info
                    chart_info = self.chart_viewer.get_chart_info()

                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("Data Points", chart_info['data_points'])
                    with col2:
                        if chart_info['date_range']['start']:
                            st.metric("Start Date", chart_info['date_range']['start'].strftime("%Y-%m-%d"))
                    with col3:
                        if chart_info['date_range']['end']:
                            st.metric("End Date", chart_info['date_range']['end'].strftime("%Y-%m-%d"))

                    # Store in session state
                    st.session_state.data_loaded = True
                    st.session_state.current_pair = selected_pair
                    st.session_state.current_timeframe = selected_timeframe

                    # Force page refresh to show new chart
                    st.rerun()
                    
                else:
                    st.error(f"❌ Failed to load data for {selected_pair} {selected_timeframe}")
        
        # Quick timeframe switcher (if data is loaded)
        if hasattr(st.session_state, 'data_loaded') and st.session_state.data_loaded:
            st.subheader(get_string("DATA_SELECTION", "quick_timeframe_switch"))

            # Use enabled timeframes from user settings
            available_common = available_timeframes  # Already filtered by enabled timeframes
            
            cols = st.columns(len(available_common))
            
            for i, tf in enumerate(available_common):
                with cols[i]:
                    if st.button(tf, key=f"tf_{tf}"):
                        with st.spinner(f"Switching to {tf}..."):
                            fig = self.chart_viewer.change_timeframe(tf)
                            if fig:
                                st.session_state.current_timeframe = tf
                                st.session_state.chart_figure = fig
                                st.rerun()
        
        return {
            'pair': selected_pair,
            'timeframe': selected_timeframe,
            'start_date': start_date,
            'end_date': end_date,
            'days_back': days_back,
            'date_option': date_option
        }
    
    def render_data_info(self):
        """Render current data information."""
        if not hasattr(st.session_state, 'data_loaded') or not st.session_state.data_loaded:
            st.info("No data loaded. Please select data parameters above.")
            return
        
        chart_info = self.chart_viewer.get_chart_info()
        
        # Current Data Info section removed - information available in Quick Analysis above
        
        # Memory usage info (direct display, no nested accordion)
        st.subheader("💾 Memory Usage")
        try:
            from ...data.data_manager import data_manager
            cache_stats = data_manager.get_cache_stats()

            col1, col2 = st.columns(2)

            with col1:
                st.metric("Cache Entries", cache_stats['entries'])
                st.metric("Cache Size (MB)", f"{cache_stats['total_size_mb']:.2f}")

            with col2:
                st.metric("Cache Usage %", f"{cache_stats['usage_percent']:.1f}%")

                if st.button("🗑️ Clear Cache"):
                    data_manager.clear_cache()
                    st.success("Cache cleared!")
                    st.rerun()

        except Exception as e:
            logger.error(f"Error displaying memory info: {str(e)}")
    
    def render_export_options(self):
        """Render data export options."""
        if not hasattr(st.session_state, 'data_loaded') or not st.session_state.data_loaded:
            return
        
        st.subheader("📤 Export Options")
        
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("📊 Export Chart as HTML"):
                try:
                    if hasattr(st.session_state, 'chart_figure'):
                        html_str = st.session_state.chart_figure.to_html()
                        st.download_button(
                            label="Download HTML",
                            data=html_str,
                            file_name=f"{st.session_state.current_pair}_{st.session_state.current_timeframe}_chart.html",
                            mime="text/html"
                        )
                except Exception as e:
                    st.error(f"Export failed: {str(e)}")
        
        with col2:
            if st.button("📋 Export Indicator Config"):
                try:
                    config_json = self.chart_viewer.export_indicator_config()
                    st.download_button(
                        label="Download Config",
                        data=config_json,
                        file_name=f"indicators_config.json",
                        mime="application/json"
                    )
                except Exception as e:
                    st.error(f"Export failed: {str(e)}")
