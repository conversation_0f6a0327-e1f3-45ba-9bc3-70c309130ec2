"""
Simple Settings Panel Component for Lionaire Platform.
Lightweight version to avoid infinite loading loops.
"""

import streamlit as st
import json
from pathlib import Path
from datetime import datetime, time
from typing import Dict, Any


class SimpleSettingsPanel:
    """
    Simple settings management panel without complex dependencies.
    """
    
    def __init__(self):
        self.project_dir = Path(__file__).parent.parent.parent
        self.config_dir = self.project_dir / 'config'
        self.settings_file = self.config_dir / 'user_settings.json'

        # Ensure config directory exists
        self.config_dir.mkdir(exist_ok=True)

    def _safe_selectbox(self, label: str, options: list, current_value: str, default_value: str, help_text: str = None, key: str = None):
        """Create a selectbox that safely handles values not in the options list."""
        try:
            # Ensure current_value is in options
            if current_value not in options:
                if current_value:  # Only add if not empty
                    options = options + [current_value]
                else:
                    current_value = default_value

            # Get index
            try:
                index = options.index(current_value)
            except ValueError:
                index = options.index(default_value) if default_value in options else 0

            return st.selectbox(
                label,
                options=options,
                index=index,
                help=help_text,
                key=key
            )
        except Exception as e:
            st.error(f"Error in selectbox {label}: {e}")
            return default_value
    
    def render(self):
        """Render the complete settings panel."""
        # Header with Back button in same line (remove duplicate title)
        col1, col2 = st.columns([3, 1])
        with col2:
            if st.button("← Back to Chart", use_container_width=True):
                st.session_state.show_settings_panel = False
                st.rerun()

        # Load current settings
        current_settings = self._load_current_settings()

        # Create tabs for different setting categories
        tab1, tab2, tab3, tab4 = st.tabs([
            "🌍 Timezone & Sessions",
            "📊 Data Loading",
            "💾 Cache Settings",
            "🎨 UI Preferences"
        ])

        with tab1:
            self._render_timezone_settings(current_settings)

        with tab2:
            self._render_data_loading_settings(current_settings)

        with tab3:
            self._render_cache_settings(current_settings)

        with tab4:
            self._render_ui_preferences(current_settings)

    def render_settings_summary_sidebar(self):
        """Render complete settings summary for sidebar in single table."""
        try:
            current_settings = self._load_current_settings()

            # Create HTML table with proper formatting
            html_content = """
<style>
.settings-table {
    width: 100%;
    font-size: 12px;
    border-collapse: collapse;
}
.settings-table td {
    padding: 2px 4px;
    border-bottom: 1px solid #ddd;
}
.settings-table .setting-name {
    font-weight: bold;
    width: 60%;
}
.settings-table .setting-value {
    font-family: monospace;
    background-color: #f0f0f0;
    padding: 1px 4px;
    border-radius: 3px;
    width: 40%;
}
</style>
<table class="settings-table">
""" + "".join([
    f'<tr><td class="setting-name">{name}</td><td class="setting-value">{value}</td></tr>'
    for name, value in [
        ("Data Timezone", current_settings.get('timezone', {}).get('data_timezone', 'UTC-5')),
        ("Display Timezone", current_settings.get('timezone', {}).get('display_timezone', 'UTC+7')),
        ("Forex Open", current_settings.get('market_sessions', {}).get('forex_open', '16:00')),
        ("Non-Forex Open", current_settings.get('market_sessions', {}).get('non_forex_open', '17:00')),
        ("Days Back Default", current_settings.get('data_loading', {}).get('days_back_default', 5)),
        ("Max Load Candles", f"{current_settings.get('data_loading', {}).get('max_candles_load', 200000):,}"),
        ("Max Display Candles", f"{current_settings.get('data_loading', {}).get('max_candles_display', 15000):,}"),
        ("Cache Size", f"{current_settings.get('cache', {}).get('max_cache_size_gb', 10)} GB"),
        ("Cache All TF", current_settings.get('data_loading', {}).get('cache_all_tf_on_load', False)),
        ("Default Timeframe", current_settings.get('ui_preferences', {}).get('default_timeframe', 'H1')),
        ("Chart Style", current_settings.get('ui_preferences', {}).get('chart_style', 'candlestick')),
        ("Remove Gaps", current_settings.get('ui_preferences', {}).get('remove_gaps', True)),
        ("Crosshair Enabled", current_settings.get('ui_preferences', {}).get('crosshair_enabled', True)),
        ("Disk Cache", current_settings.get('cache', {}).get('enable_disk_cache', True)),
        ("Cache Compression", current_settings.get('cache', {}).get('cache_compression', 'lz4'))
    ]
]) + "</table>"

            st.markdown(html_content, unsafe_allow_html=True)

        except Exception as e:
            st.error(f"Error loading settings summary: {e}")
            st.markdown("**Settings summary unavailable**")

    def _load_current_settings(self) -> Dict[str, Any]:
        """Load current settings from file or return defaults."""
        default_settings = {
            'version': '1.0.0',
            'timezone': {
                'data_timezone': 'UTC-5',
                'display_timezone': 'UTC+7'
            },
            'market_sessions': {
                'forex_open': '16:00',
                'non_forex_open': '17:00',
                'non_forex_symbols': ['XAUUSD', 'SPXUSD', 'NSXUSD']
            },
            'data_loading': {
                'days_back_default': 5,
                'days_back_minimum': 1,
                'max_candles_load': 200000,
                'max_candles_display': 15000,
                'enabled_timeframes': ['M1', 'M5', 'M15', 'H1', 'H4', 'D1'],
                'disabled_timeframes': ['M30', 'W1', 'MN1'],
                'cache_all_tf_on_load': False
            },
            'cache': {
                'max_cache_size_gb': 10,
                'insufficient_data_behavior': 'show_warning',
                'enable_disk_cache': True,
                'cache_compression': 'lz4'
            },
            'ui_preferences': {
                'default_timeframe': 'H1',
                'chart_style': 'candlestick',
                'remove_gaps': True,
                'crosshair_enabled': True,
                'crosshair_vertical': True,
                'crosshair_horizontal': True
            }
        }
        
        try:
            if self.settings_file.exists():
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    loaded_settings = json.load(f)
                
                # Merge with defaults
                for key, value in default_settings.items():
                    if key not in loaded_settings:
                        loaded_settings[key] = value
                    elif isinstance(value, dict):
                        for subkey, subvalue in value.items():
                            if subkey not in loaded_settings[key]:
                                loaded_settings[key][subkey] = subvalue
                
                return loaded_settings
            else:
                return default_settings
                
        except Exception as e:
            st.warning(f"Could not load settings: {e}")
            return default_settings
    
    def _render_timezone_settings(self, current_settings: Dict[str, Any]):
        """Render timezone and market session settings."""
        st.write("**Timezone Configuration**")
        
        tz_settings = current_settings.get('timezone', {})
        market_settings = current_settings.get('market_sessions', {})
        
        # Data timezone
        data_tz_options = ["UTC-8", "UTC-7", "UTC-6", "UTC-5", "UTC-4", "UTC-3", "UTC-2", "UTC-1", "UTC", "UTC+1", "UTC+2", "UTC+3", "UTC+4", "UTC+5", "UTC+6", "UTC+7", "UTC+8", "UTC+9"]
        current_data_tz = tz_settings.get('data_timezone', 'UTC-5')

        data_tz = self._safe_selectbox(
            "Data Timezone",
            data_tz_options,
            current_data_tz,
            'UTC-5',
            "Timezone of the historical data (histdata.com uses EST = UTC-5)",
            key="data_timezone"
        )
        
        # Display timezone
        display_tz_options = ["UTC-8", "UTC-7", "UTC-6", "UTC-5", "UTC-4", "UTC-3", "UTC-2", "UTC-1", "UTC", "UTC+1", "UTC+2", "UTC+3", "UTC+4", "UTC+5", "UTC+6", "UTC+7", "UTC+8", "UTC+9"]
        current_display_tz = tz_settings.get('display_timezone', 'UTC+7')

        display_tz = self._safe_selectbox(
            "Display Timezone",
            display_tz_options,
            current_display_tz,
            'UTC+7',
            "Timezone for displaying times in the interface",
            key="display_timezone"
        )
        
        st.write("**Market Session Configuration**")
        
        # Parse current times
        forex_open_str = market_settings.get('forex_open', '16:00')
        non_forex_open_str = market_settings.get('non_forex_open', '17:00')
        
        forex_hour, forex_min = map(int, forex_open_str.split(':'))
        non_forex_hour, non_forex_min = map(int, non_forex_open_str.split(':'))
        
        # Forex market open
        forex_open = st.time_input(
            "Forex Market Open",
            value=time(forex_hour, forex_min),
            help="Daily market open time for Forex pairs"
        )
        
        # Non-forex market open
        non_forex_open = st.time_input(
            "Non-Forex Market Open",
            value=time(non_forex_hour, non_forex_min),
            help="Daily market open time for Gold, Indices, etc."
        )
        
        # Non-forex symbols
        st.write("**Non-Forex Symbols**")
        non_forex_symbols = st.multiselect(
            "Select Non-Forex Instruments",
            options=["XAUUSD", "SPXUSD", "NSXUSD", "BTCUSD", "ETHUSD"],
            default=market_settings.get('non_forex_symbols', ["XAUUSD", "SPXUSD", "NSXUSD"]),
            help="Instruments that use Non-Forex market session times"
        )
        
        # Display session boundaries preview
        self._show_session_preview(forex_open.strftime("%H:%M"), non_forex_open.strftime("%H:%M"))
        
        # Save button
        if st.button("💾 Save Timezone Settings", key="save_timezone"):
            settings_data = {
                'timezone': {
                    'data_timezone': data_tz,
                    'display_timezone': display_tz
                },
                'market_sessions': {
                    'forex_open': forex_open.strftime("%H:%M"),
                    'non_forex_open': non_forex_open.strftime("%H:%M"),
                    'non_forex_symbols': non_forex_symbols
                }
            }
            
            if self._save_settings(settings_data):
                st.success("✅ Timezone settings saved successfully!")
            else:
                st.error("❌ Failed to save timezone settings")
    
    def _render_data_loading_settings(self, current_settings: Dict[str, Any]):
        """Render data loading configuration."""
        st.write("**Data Loading Strategy**")
        
        data_settings = current_settings.get('data_loading', {})
        
        # N Days Back setting
        days_back = st.number_input(
            "Default Days Back",
            min_value=1,
            max_value=30,
            value=data_settings.get('days_back_default', 5),
            help="Default number of days to load when opening a chart"
        )
        
        # Max candles settings
        col1, col2 = st.columns(2)
        
        with col1:
            max_load = st.number_input(
                "Max Candles to Load",
                min_value=10000,
                max_value=1000000,
                value=data_settings.get('max_candles_load', 200000),
                step=10000,
                help="Maximum candles to load from files"
            )
        
        with col2:
            max_display = st.number_input(
                "Max Candles to Display",
                min_value=1000,
                max_value=50000,
                value=data_settings.get('max_candles_display', 15000),
                step=1000,
                help="Maximum candles to show on chart"
            )
        
        # Enabled timeframes
        st.write("**Enabled Timeframes**")

        all_timeframes = ["M1", "M5", "M15", "M30", "H1", "H4", "D1", "W1", "MN1"]
        default_enabled = data_settings.get('enabled_timeframes', ["M1", "M5", "M15", "H1", "H4", "D1"])

        enabled_tfs = st.multiselect(
            "Select Enabled Timeframes",
            options=all_timeframes,
            default=default_enabled,
            help="Timeframes available in the interface"
        )

        # Cache all TF option
        cache_all_tf = st.checkbox(
            "Cache All Timeframes on Load",
            value=data_settings.get('cache_all_tf_on_load', False),
            help="Pre-cache all enabled timeframes when loading data (uses more memory)"
        )

        # Insufficient data behavior
        st.write("**Insufficient Data Handling**")
        insufficient_behavior_options = ["show_warning", "auto_load"]
        current_behavior = data_settings.get('insufficient_data_behavior', 'show_warning')

        insufficient_behavior = self._safe_selectbox(
            "When insufficient data is available",
            insufficient_behavior_options,
            current_behavior,
            'show_warning',
            "How to handle cases where requested data range is not available",
            key="insufficient_data_behavior"
        )
        
        # Save button
        if st.button("💾 Save Data Loading Settings", key="save_data_loading"):
            settings_data = {
                'data_loading': {
                    'days_back_default': days_back,
                    'days_back_minimum': 1,
                    'max_candles_load': max_load,
                    'max_candles_display': max_display,
                    'enabled_timeframes': enabled_tfs,
                    'disabled_timeframes': [tf for tf in all_timeframes if tf not in enabled_tfs],
                    'cache_all_tf_on_load': cache_all_tf
                },
                'cache': {
                    'insufficient_data_behavior': insufficient_behavior
                }
            }
            
            if self._save_settings(settings_data):
                st.success("✅ Data loading settings saved successfully!")
                # Force reload of user settings in other components
                if 'user_settings_reload_trigger' not in st.session_state:
                    st.session_state.user_settings_reload_trigger = 0
                st.session_state.user_settings_reload_trigger += 1
                # Trigger rerun to update UI immediately
                st.rerun()
            else:
                st.error("❌ Failed to save data loading settings")
    
    def _render_cache_settings(self, current_settings: Dict[str, Any]):
        """Render cache configuration."""
        st.write("**Cache Configuration**")
        
        cache_settings = current_settings.get('cache', {})
        
        # Max cache size
        cache_size_gb = st.slider(
            "Maximum Cache Size (GB)",
            min_value=1,
            max_value=50,
            value=cache_settings.get('max_cache_size_gb', 10),
            help="Maximum disk space for cache storage"
        )
        
        # Cache compression
        compression_options = ["lz4", "snappy", "gzip", "none"]
        current_compression = cache_settings.get('cache_compression', 'lz4')

        compression = self._safe_selectbox(
            "Cache Compression",
            compression_options,
            current_compression,
            'lz4',
            "Compression method: LZ4 (fastest), Snappy (balanced), Gzip (smallest), None (no compression)",
            key="cache_compression"
        )

        # Show compression info
        compression_info = {
            'lz4': '🚀 Fastest compression/decompression (recommended)',
            'snappy': '⚖️ Balanced speed and compression ratio',
            'gzip': '📦 Best compression ratio but slower',
            'none': '⚡ No compression (fastest but largest files)'
        }
        if compression in compression_info:
            st.info(compression_info[compression])
        
        # Enable disk cache
        enable_disk_cache = st.checkbox(
            "Enable Disk Cache",
            value=cache_settings.get('enable_disk_cache', True),
            help="Store cache on disk for persistence across sessions"
        )
        
        # Save button
        if st.button("💾 Save Cache Settings", key="save_cache"):
            settings_data = {
                'cache': {
                    'max_cache_size_gb': cache_size_gb,
                    'cache_compression': compression,
                    'enable_disk_cache': enable_disk_cache
                }
            }
            
            if self._save_settings(settings_data):
                st.success("✅ Cache settings saved successfully!")
            else:
                st.error("❌ Failed to save cache settings")
    
    def _render_ui_preferences(self, current_settings: Dict[str, Any]):
        """Render UI preferences."""
        st.write("**Chart Preferences**")
        
        ui_settings = current_settings.get('ui_preferences', {})
        
        # Default timeframe
        tf_options = ["M1", "M5", "M15", "M30", "H1", "H4", "D1", "W1", "MN1"]
        current_tf = ui_settings.get('default_timeframe', 'H1')

        default_tf = self._safe_selectbox(
            "Default Timeframe",
            tf_options,
            current_tf,
            'H1',
            "Default timeframe when opening charts",
            key="default_timeframe"
        )
        
        # Chart style
        style_options = ["candlestick", "ohlc", "line"]
        current_style = ui_settings.get('chart_style', 'candlestick')

        chart_style = self._safe_selectbox(
            "Default Chart Style",
            style_options,
            current_style,
            'candlestick',
            "Default chart visualization style",
            key="chart_style"
        )
        
        # Remove gaps
        remove_gaps = st.checkbox(
            "Remove Weekend Gaps",
            value=ui_settings.get('remove_gaps', True),
            help="Remove gaps in chart for better visualization"
        )

        # Crosshair settings
        st.write("**Crosshair Settings**")

        col1, col2 = st.columns(2)

        with col1:
            crosshair_enabled = st.checkbox(
                "Enable Crosshair",
                value=ui_settings.get('crosshair_enabled', True),
                help="Show crosshair on chart hover"
            )

            crosshair_vertical = st.checkbox(
                "Vertical Line",
                value=ui_settings.get('crosshair_vertical', True),
                help="Show vertical crosshair line",
                disabled=not crosshair_enabled
            )

        with col2:
            crosshair_horizontal = st.checkbox(
                "Horizontal Line",
                value=ui_settings.get('crosshair_horizontal', True),
                help="Show horizontal crosshair line with price level",
                disabled=not crosshair_enabled
            )
        
        # Save button
        if st.button("💾 Save UI Preferences", key="save_ui"):
            settings_data = {
                'ui_preferences': {
                    'default_timeframe': default_tf,
                    'chart_style': chart_style,
                    'remove_gaps': remove_gaps,
                    'crosshair_enabled': crosshair_enabled,
                    'crosshair_vertical': crosshair_vertical,
                    'crosshair_horizontal': crosshair_horizontal
                }
            }
            
            if self._save_settings(settings_data):
                st.success("✅ UI preferences saved successfully!")
            else:
                st.error("❌ Failed to save UI preferences")
    
    def _show_session_preview(self, forex_open: str, non_forex_open: str):
        """Show session boundary preview in accordion."""
        with st.expander("📅 Session Boundaries Preview", expanded=False):
            col1, col2 = st.columns(2)

            with col1:
                st.write("**Forex (EURUSD)**")
                forex_sessions = self._calculate_sessions(forex_open)
                for i, session in enumerate(forex_sessions, 1):
                    st.write(f"H4-{i}: {session}")

            with col2:
                st.write("**Non-Forex (XAUUSD)**")
                non_forex_sessions = self._calculate_sessions(non_forex_open)
                for i, session in enumerate(non_forex_sessions, 1):
                    st.write(f"H4-{i}: {session}")
    
    def _calculate_sessions(self, market_open_str: str) -> list:
        """Calculate H4 session boundaries."""
        try:
            hour, minute = map(int, market_open_str.split(':'))
            sessions = []
            
            for i in range(6):
                start_hour = (hour + i * 4) % 24
                end_hour = (hour + (i + 1) * 4) % 24
                
                if end_hour == hour:  # Last session
                    sessions.append(f"{start_hour:02d}:{minute:02d} → Close")
                else:
                    sessions.append(f"{start_hour:02d}:{minute:02d} → {end_hour:02d}:{minute:02d}")
            
            return sessions
            
        except Exception:
            return ["Error calculating sessions"]
    
    def _save_settings(self, settings_data: Dict[str, Any]) -> bool:
        """Save settings to JSON file."""
        try:
            # Load existing settings or create complete defaults
            if self.settings_file.exists():
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    existing_settings = json.load(f)
            else:
                # Create complete default settings when file doesn't exist
                existing_settings = {
                    'version': '1.0.0',
                    'created': datetime.now().isoformat(),
                    'timezone': {
                        'data_timezone': 'UTC-5',
                        'display_timezone': 'UTC+7'
                    },
                    'market_sessions': {
                        'forex_open': '16:00',
                        'non_forex_open': '17:00',
                        'non_forex_symbols': ['XAUUSD', 'SPXUSD', 'NSXUSD']
                    },
                    'data_loading': {
                        'days_back_default': 5,
                        'days_back_minimum': 1,
                        'max_candles_load': 200000,
                        'max_candles_display': 15000,
                        'enabled_timeframes': ['M1', 'M5', 'M15', 'H1', 'H4', 'D1'],
                        'disabled_timeframes': ['M30', 'W1', 'MN1'],
                        'cache_all_tf_on_load': False
                    },
                    'cache': {
                        'max_cache_size_gb': 10,
                        'insufficient_data_behavior': 'show_warning',
                        'enable_disk_cache': True,
                        'cache_compression': 'lz4'
                    },
                    'ui_preferences': {
                        'default_timeframe': 'H1',
                        'chart_style': 'candlestick',
                        'remove_gaps': True,
                        'crosshair_enabled': True,
                        'crosshair_vertical': True,
                        'crosshair_horizontal': True
                    }
                }
            
            # Merge new settings
            for key, value in settings_data.items():
                if key in existing_settings and isinstance(existing_settings[key], dict) and isinstance(value, dict):
                    existing_settings[key].update(value)
                else:
                    existing_settings[key] = value
            
            # Update timestamp
            existing_settings['last_updated'] = datetime.now().isoformat()
            
            # Save to file
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(existing_settings, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            st.error(f"Error saving settings: {e}")
            return False
    

