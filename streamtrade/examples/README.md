# Lionaire Platform - Examples

This directory contains example code and demonstrations for various features of the Lionaire platform.

## 📁 Available Examples

### 🌍 `strings_usage_example.py`
**Strings/Localization System Demo**

Comprehensive demonstration of the Lionaire strings and localization system, including:

- **Basic string retrieval** from different categories
- **String formatting** with parameters
- **Project information** and app configuration
- **UI component usage** examples
- **Customization** possibilities
- **Localization** examples for multiple languages

**Run the example:**
```bash
cd streamtrade
python examples/strings_usage_example.py
```

**Features demonstrated:**
- Direct access to string categories
- `get_string()` function usage with fallbacks
- String formatting for dynamic content
- Project metadata retrieval
- UI component integration patterns
- Custom branding examples
- Multi-language localization setup

## 🚀 Running Examples

### Prerequisites
1. Activate your Python environment:
   ```bash
   source /home/<USER>/Learn/python/notebooks/venv/bin/activate
   ```

2. Navigate to the streamtrade directory:
   ```bash
   cd streamtrade
   ```

3. Run any example:
   ```bash
   python examples/<example_name>.py
   ```

## 📚 Learning Path

### 1. **Start with Strings System**
Run `strings_usage_example.py` to understand how the localization system works and how to customize UI text.

### 2. **Explore Integration**
See how strings are integrated into actual UI components by examining the GUI components in `streamtrade/gui/components/`.

### 3. **Customize for Your Needs**
Use the examples as templates to create your own customizations or translations.

## 🔧 Creating New Examples

When creating new examples, follow these guidelines:

### **File Structure**
```python
"""
Brief description of what this example demonstrates.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Your imports here
from streamtrade.config.strings import get_string, strings

def demonstrate_feature():
    """Demonstrate specific feature."""
    print("=== Feature Demo ===")
    # Your demo code here
    print()

if __name__ == "__main__":
    print("🌍 Lionaire Platform - Feature Demo")
    print("=" * 50)
    demonstrate_feature()
    print("✅ Demo completed!")
```

### **Documentation**
- Add clear docstrings explaining what the example demonstrates
- Include usage instructions in this README
- Add comments explaining complex concepts
- Provide expected output examples

### **Testing**
- Ensure examples run without errors
- Test with different configurations
- Verify output is meaningful and educational

## 🌍 Localization Examples

### **Creating Custom Language Files**

**Example: Indonesian (Bahasa Indonesia)**
```python
# streamtrade/config/strings_id.py
STRINGS_ID = {
    'PROJECT': {
        'name': 'Lionaire',
        'full_name': 'Platform Lionaire'
    },
    'APP': {
        'title': '📈 Platform Lionaire',
        'subtitle': 'Platform Analisis & Backtesting Trading Lanjutan'
    },
    'DATA_SELECTION': {
        'title': '📊 Pemilihan Data',
        'currency_pair': 'Pasangan Mata Uang',
        'timeframe': 'Kerangka Waktu',
        'date_range': '📅 Rentang Tanggal'
    },
    'CHART': {
        'title': '📈 Grafik Harga',
        'refresh_chart': '🔄 Segarkan Grafik',
        'auto_scale': '🎯 Skala Otomatis'
    },
    'MESSAGES': {
        'loading': 'Memuat...',
        'data_loaded': 'Data berhasil dimuat!',
        'application_error': 'Kesalahan aplikasi: {error}'
    }
}
```

**Example: Spanish (Español)**
```python
# streamtrade/config/strings_es.py
STRINGS_ES = {
    'PROJECT': {
        'name': 'Lionaire',
        'full_name': 'Plataforma Lionaire'
    },
    'APP': {
        'title': '📈 Plataforma Lionaire',
        'subtitle': 'Plataforma Avanzada de Análisis y Backtesting de Trading'
    },
    'DATA_SELECTION': {
        'title': '📊 Selección de Datos',
        'currency_pair': 'Par de Divisas',
        'timeframe': 'Marco Temporal',
        'date_range': '📅 Rango de Fechas'
    },
    'CHART': {
        'title': '📈 Gráfico de Precios',
        'refresh_chart': '🔄 Actualizar Gráfico',
        'auto_scale': '🎯 Escala Automática'
    },
    'MESSAGES': {
        'loading': 'Cargando...',
        'data_loaded': '¡Datos cargados exitosamente!',
        'application_error': 'Error de aplicación: {error}'
    }
}
```

## 🎨 Customization Examples

### **Custom Branding**
```python
# Custom strings for different branding
CUSTOM_BRAND = {
    'PROJECT': {
        'name': 'TradeMaster',
        'full_name': 'TradeMaster Pro Platform'
    },
    'APP': {
        'title': '🚀 TradeMaster Pro',
        'subtitle': 'Professional Trading & Analysis Suite'
    }
}
```

### **Theme-Specific Strings**
```python
# Dark theme specific strings
DARK_THEME_STRINGS = {
    'CHART': {
        'title': '🌙 Price Chart (Dark Mode)',
        'theme_indicator': '🌙'
    }
}

# Light theme specific strings  
LIGHT_THEME_STRINGS = {
    'CHART': {
        'title': '☀️ Price Chart (Light Mode)',
        'theme_indicator': '☀️'
    }
}
```

## 📖 Related Documentation

- **[008-strings-localization-rebrand.md](../docs/008-strings-localization-rebrand.md)** - Complete implementation documentation
- **[strings.py](../config/strings.py)** - Main strings configuration file
- **[GUI Components](../gui/components/)** - See how strings are used in practice

## 🤝 Contributing Examples

To contribute new examples:

1. Create a new Python file in this directory
2. Follow the file structure guidelines above
3. Add documentation to this README
4. Test your example thoroughly
5. Submit a pull request with clear description

## 📞 Support

For questions about examples or the strings system:
- Check the main documentation in `streamtrade/docs/`
- Review the source code in `streamtrade/config/strings.py`
- Run the examples to see working implementations
