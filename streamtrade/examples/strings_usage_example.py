"""
Example usage of the Lionaire strings/localization system.
This file demonstrates how to use the strings system for UI text management.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from streamtrade.config.strings import get_string, strings, get_project_info, get_app_config


def demonstrate_basic_usage():
    """Demonstrate basic string retrieval."""
    print("=== Basic String Usage ===")
    
    # Direct access to strings
    print(f"Project Name: {strings.PROJECT['name']}")
    print(f"App Title: {strings.APP['title']}")
    print(f"Data Selection Title: {strings.DATA_SELECTION['title']}")
    
    print()


def demonstrate_get_string_function():
    """Demonstrate get_string function usage."""
    print("=== Using get_string() Function ===")
    
    # Using get_string for better error handling
    title = get_string("APP", "title")
    subtitle = get_string("APP", "subtitle")
    
    print(f"Title: {title}")
    print(f"Subtitle: {subtitle}")
    
    # Fallback for missing keys
    missing = get_string("APP", "nonexistent_key")
    print(f"Missing key fallback: {missing}")
    
    print()


def demonstrate_string_formatting():
    """Demonstrate string formatting with parameters."""
    print("=== String Formatting ===")
    
    # Error message with parameter
    error_msg = get_string("MESSAGES", "application_error", error="Connection timeout")
    print(f"Error Message: {error_msg}")
    
    # Timeframe switching message
    switching_msg = get_string("MESSAGES", "switching_timeframe", timeframe="H4")
    print(f"Switching Message: {switching_msg}")
    
    # Export filename with parameters
    filename = get_string("FILES", "export_filename", 
                         pair="EURUSD", timeframe="H1", date="2025-06-27")
    print(f"Export Filename: {filename}")
    
    print()


def demonstrate_project_info():
    """Demonstrate project information retrieval."""
    print("=== Project Information ===")
    
    project_info = get_project_info()
    for key, value in project_info.items():
        print(f"{key.title()}: {value}")
    
    print()


def demonstrate_app_config():
    """Demonstrate application configuration."""
    print("=== App Configuration ===")
    
    app_config = get_app_config()
    for key, value in app_config.items():
        print(f"{key}: {value}")
    
    print()


def demonstrate_all_categories():
    """Demonstrate all available string categories."""
    print("=== All String Categories ===")
    
    categories = [
        "PROJECT", "APP", "HEADER", "DATA_SELECTION", 
        "CHART", "CHART_STYLES", "CHART_THEMES",
        "INDICATORS", "INDICATOR_CATEGORIES",
        "MEMORY", "MESSAGES", "BUTTONS", "FILES"
    ]
    
    for category in categories:
        if hasattr(strings, category):
            category_dict = getattr(strings, category)
            print(f"{category}: {len(category_dict)} strings")
            
            # Show first few keys as examples
            keys = list(category_dict.keys())[:3]
            for key in keys:
                value = category_dict[key]
                if len(value) > 50:
                    value = value[:47] + "..."
                print(f"  {key}: {value}")
            
            if len(category_dict) > 3:
                print(f"  ... and {len(category_dict) - 3} more")
            print()


def demonstrate_ui_usage():
    """Demonstrate how strings would be used in UI components."""
    print("=== UI Component Usage Examples ===")
    
    # Simulating Streamlit usage
    print("# Header Component")
    print(f"st.markdown(f'<h1>{strings.APP['title']}</h1>')")
    print(f"st.markdown(f'<div>{strings.APP['subtitle']}</div>')")
    print()
    
    print("# Data Selection Component")
    print(f"st.subheader(get_string('DATA_SELECTION', 'title'))")
    print(f"st.selectbox(get_string('DATA_SELECTION', 'currency_pair'), ...)")
    print(f"st.selectbox(get_string('DATA_SELECTION', 'timeframe'), ...)")
    print()
    
    print("# Chart Component")
    print(f"st.subheader(get_string('CHART', 'title'))")
    print(f"st.button(get_string('CHART', 'refresh_chart'))")
    print(f"st.button(get_string('CHART', 'auto_scale'))")
    print()
    
    print("# Error Handling")
    print("try:")
    print("    # some operation")
    print("except Exception as e:")
    print("    st.error(get_string('MESSAGES', 'application_error', error=str(e)))")
    print()


def demonstrate_customization():
    """Demonstrate how to customize strings."""
    print("=== Customization Examples ===")
    
    print("To customize strings, you can:")
    print("1. Modify streamtrade/config/strings.py directly")
    print("2. Create a custom strings file and import it")
    print("3. Override specific strings at runtime")
    print()
    
    print("Example custom strings:")
    print("```python")
    print("# Custom strings for different branding")
    print("custom_strings = {")
    print("    'PROJECT': {")
    print("        'name': 'MyTrader',")
    print("        'full_name': 'MyTrader Pro Platform'")
    print("    },")
    print("    'APP': {")
    print("        'title': '📊 MyTrader Pro',")
    print("        'subtitle': 'Professional Trading Platform'")
    print("    }")
    print("}")
    print("```")
    print()


def demonstrate_localization():
    """Demonstrate localization possibilities."""
    print("=== Localization Examples ===")
    
    print("For Indonesian localization, create strings_id.py:")
    print("```python")
    print("STRINGS_ID = {")
    print("    'APP': {")
    print("        'title': '📈 Platform Lionaire',")
    print("        'subtitle': 'Platform Analisis & Backtesting Trading Lanjutan'")
    print("    },")
    print("    'DATA_SELECTION': {")
    print("        'title': '📊 Pemilihan Data',")
    print("        'currency_pair': 'Pasangan Mata Uang',")
    print("        'timeframe': 'Kerangka Waktu'")
    print("    },")
    print("    'MESSAGES': {")
    print("        'loading': 'Memuat...',")
    print("        'data_loaded': 'Data berhasil dimuat!'")
    print("    }")
    print("}")
    print("```")
    print()


if __name__ == "__main__":
    print("🌍 Lionaire Platform - Strings/Localization System Demo")
    print("=" * 60)
    print()
    
    demonstrate_basic_usage()
    demonstrate_get_string_function()
    demonstrate_string_formatting()
    demonstrate_project_info()
    demonstrate_app_config()
    demonstrate_all_categories()
    demonstrate_ui_usage()
    demonstrate_customization()
    demonstrate_localization()
    
    print("✅ Demo completed successfully!")
    print("📚 For more information, see: streamtrade/docs/008-strings-localization-rebrand.md")
