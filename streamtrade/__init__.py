"""
Lionaire Platform - Advanced Trading and Backtesting System

A comprehensive platform for forex trading analysis, backtesting, and visualization.
"""

from .config.strings import get_project_info

# Get project info from strings
_project_info = get_project_info()

__version__ = _project_info["version"]
__author__ = _project_info["author"]
__description__ = _project_info["description"]

# Core imports
from .config import Settings, setup_logging
from .data import DataManager, DataLoader, TimeframeConverter
from .indicators import TechnicalIndicators, IndicatorManager
from .visualization import <PERSON><PERSON>iewer, PlotlyCharts
from .gui import LionaireApp

# Initialize logging
setup_logging(level="INFO", console_output=True)

__all__ = [
    'Settings',
    'setup_logging',
    'DataManager',
    'DataLoader',
    'TimeframeConverter',
    'TechnicalIndicators',
    'IndicatorManager',
    'ChartViewer',
    'PlotlyCharts',
    'StreamTradeApp'
]
