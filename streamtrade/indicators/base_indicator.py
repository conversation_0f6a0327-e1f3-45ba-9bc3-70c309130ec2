"""
Base indicator class for StreamTrade platform.
"""

from abc import ABC, abstractmethod
import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field

from ..config.logging_config import get_logger

logger = get_logger(__name__)


@dataclass
class IndicatorParameter:
    """Parameter definition for indicators."""
    name: str
    type: type
    default: Any
    min_value: Optional[Union[int, float]] = None
    max_value: Optional[Union[int, float]] = None
    description: str = ""
    options: Optional[List[Any]] = None  # For dropdown/select parameters
    is_color: bool = False  # Flag for color parameters


@dataclass
class IndicatorResult:
    """Result from indicator calculation."""
    name: str
    data: Dict[str, pd.Series]
    parameters: Dict[str, Any]
    metadata: Dict[str, Any] = field(default_factory=dict)


class BaseIndicator(ABC):
    """
    Base class for all technical indicators.
    
    Features:
    - Parameter validation and management
    - Standardized calculation interface
    - Result formatting and metadata
    - Error handling and logging
    """
    
    def __init__(self):
        self.name = self.__class__.__name__
        self.parameters = self._define_parameters()
        self.description = self._get_description()
        self.category = self._get_category()
        
        logger.debug(f"Initialized indicator: {self.name}")
    
    @abstractmethod
    def _define_parameters(self) -> List[IndicatorParameter]:
        """
        Define the parameters for this indicator.
        
        Returns:
            List of IndicatorParameter objects
        """
        pass
    
    @abstractmethod
    def _calculate(self, data: pd.DataFrame, **kwargs) -> Dict[str, pd.Series]:
        """
        Calculate the indicator values.
        
        Args:
            data: OHLCV DataFrame with datetime index
            **kwargs: Parameter values
            
        Returns:
            Dictionary with indicator series (e.g., {'sma': series})
        """
        pass
    
    @abstractmethod
    def _get_description(self) -> str:
        """Get indicator description."""
        pass
    
    @abstractmethod
    def _get_category(self) -> str:
        """Get indicator category (trend, momentum, volatility, etc.)."""
        pass
    
    def get_parameter_info(self) -> Dict[str, Dict[str, Any]]:
        """
        Get parameter information for UI generation.
        
        Returns:
            Dictionary with parameter details
        """
        param_info = {}
        
        for param in self.parameters:
            param_info[param.name] = {
                'type': param.type.__name__,
                'default': param.default,
                'min_value': param.min_value,
                'max_value': param.max_value,
                'description': param.description,
                'options': param.options,
                'is_color': param.is_color
            }
        
        return param_info
    
    def validate_parameters(self, **kwargs) -> Dict[str, Any]:
        """
        Validate and process parameters.
        
        Args:
            **kwargs: Parameter values to validate
            
        Returns:
            Validated parameters dictionary
            
        Raises:
            ValueError: If parameter validation fails
        """
        validated = {}
        
        for param in self.parameters:
            value = kwargs.get(param.name, param.default)
            
            # Type validation
            if not isinstance(value, param.type):
                try:
                    value = param.type(value)
                except (ValueError, TypeError):
                    raise ValueError(f"Parameter {param.name} must be of type {param.type.__name__}")
            
            # Range validation
            if param.min_value is not None and value < param.min_value:
                raise ValueError(f"Parameter {param.name} must be >= {param.min_value}")
            
            if param.max_value is not None and value > param.max_value:
                raise ValueError(f"Parameter {param.name} must be <= {param.max_value}")
            
            # Options validation
            if param.options is not None and value not in param.options:
                raise ValueError(f"Parameter {param.name} must be one of {param.options}")
            
            validated[param.name] = value
        
        return validated
    
    def calculate(self, data: pd.DataFrame, **kwargs) -> IndicatorResult:
        """
        Calculate indicator with validation and error handling.
        
        Args:
            data: OHLCV DataFrame
            **kwargs: Parameter values
            
        Returns:
            IndicatorResult object
        """
        try:
            # Validate input data
            if not self._validate_input_data(data):
                raise ValueError("Invalid input data format")
            
            # Validate parameters
            validated_params = self.validate_parameters(**kwargs)
            
            # Calculate indicator
            logger.debug(f"Calculating {self.name} with parameters: {validated_params}")
            
            result_data = self._calculate(data, **validated_params)
            
            # Validate result
            if not isinstance(result_data, dict):
                raise ValueError("Indicator calculation must return a dictionary")
            
            # Create result object
            result = IndicatorResult(
                name=self.name,
                data=result_data,
                parameters=validated_params,
                metadata={
                    'description': self.description,
                    'category': self.category,
                    'data_points': len(data),
                    'calculation_success': True
                }
            )
            
            logger.debug(f"Successfully calculated {self.name}")
            return result
            
        except Exception as e:
            logger.error(f"Error calculating {self.name}: {str(e)}")
            
            # Return empty result with error info
            return IndicatorResult(
                name=self.name,
                data={},
                parameters=kwargs,
                metadata={
                    'description': self.description,
                    'category': self.category,
                    'error': str(e),
                    'calculation_success': False
                }
            )
    
    def _validate_input_data(self, data: pd.DataFrame) -> bool:
        """
        Validate input data format.
        
        Args:
            data: Input DataFrame
            
        Returns:
            True if valid, False otherwise
        """
        try:
            # Check if DataFrame is not empty
            if data.empty:
                logger.warning("Input data is empty")
                return False
            
            # Check for required columns
            required_columns = ['open', 'high', 'low', 'close']
            missing_columns = set(required_columns) - set(data.columns)
            
            if missing_columns:
                logger.warning(f"Missing required columns: {missing_columns}")
                return False
            
            # Check for datetime index
            if not isinstance(data.index, pd.DatetimeIndex):
                logger.warning("Data must have datetime index")
                return False
            
            # Check for numeric data
            for col in required_columns:
                if not pd.api.types.is_numeric_dtype(data[col]):
                    logger.warning(f"Column {col} must be numeric")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating input data: {str(e)}")
            return False
    
    def get_info(self) -> Dict[str, Any]:
        """
        Get complete indicator information.
        
        Returns:
            Dictionary with indicator details
        """
        return {
            'name': self.name,
            'description': self.description,
            'category': self.category,
            'parameters': self.get_parameter_info()
        }
    
    def __str__(self) -> str:
        return f"{self.name} ({self.category})"
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}: {self.name}>"
