"""
User Settings System for Lionaire Platform.
Manages user-configurable preferences with project-local storage.
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime
import threading

from .logging_config import get_logger

logger = get_logger(__name__)


class UserSettings:
    """
    User settings management with project-local storage.
    
    Features:
    - Project-local configuration storage in "./config/" directory
    - Timezone and market session configuration
    - Data loading strategy settings
    - Cache management settings
    - Thread-safe operations
    """
    
    def __init__(self):
        # Use project-local config directory
        self.project_dir = Path(__file__).parent.parent
        self.config_dir = self.project_dir / 'config'
        self.settings_file = self.config_dir / 'user_settings.json'
        
        # Ensure config directory exists
        self.config_dir.mkdir(exist_ok=True)
        
        # Thread safety
        self._lock = threading.Lock()
        
        # Default settings
        self.default_settings = {
            'timezone': {
                'data_timezone': 'UTC-5',  # EST without DST
                'display_timezone': 'UTC+7'  # Asia/Jakarta
            },
            'market_sessions': {
                'forex_open': '16:00',  # Forex market open time
                'non_forex_open': '17:00',  # Non-forex market open time
                'non_forex_symbols': ['XAUUSD', 'SPXUSD', 'NSXUSD']
            },
            'data_loading': {
                'days_back_default': 5,  # Default days to load back
                'days_back_minimum': 1,  # Minimum days allowed
                'max_candles_load': 200000,  # Max candles to load from files
                'max_candles_display': 15000,  # Max candles to display on chart
                'enabled_timeframes': ['M1', 'M5', 'M15', 'H1', 'H4', 'D1'],  # Default enabled TFs
                'disabled_timeframes': ['M30', 'W1', 'MN1'],  # Default disabled TFs
                'cache_all_tf_on_load': False  # Cache all timeframes on initial load
            },
            'cache': {
                'max_cache_size_gb': 10,  # Maximum cache size in GB
                'insufficient_data_behavior': 'show_warning',  # 'show_warning' or 'auto_load'
                'enable_disk_cache': True,  # Enable disk-based caching
                'cache_compression': 'snappy'  # Parquet compression method
            },
            'ui_preferences': {
                'default_timeframe': 'H1',
                'chart_style': 'candlestick',  # 'candlestick', 'ohlc', 'line'
                'remove_gaps': True,  # Remove weekend gaps by default
                'crosshair_enabled': True,
                'crosshair_vertical': True,
                'crosshair_horizontal': True
            },
            'version': '1.0.0',
            'last_updated': datetime.now().isoformat()
        }
        
        # Current settings (loaded from file or defaults)
        self.settings = {}
        
        # Load existing settings or create defaults
        self.load_settings()
        
        logger.info(f"UserSettings initialized with config file: {self.settings_file}")
    
    def load_settings(self) -> bool:
        """
        Load settings from file or create defaults.
        
        Returns:
            True if loaded from file, False if created defaults
        """
        with self._lock:
            try:
                if self.settings_file.exists():
                    with open(self.settings_file, 'r', encoding='utf-8') as f:
                        loaded_settings = json.load(f)
                    
                    # Merge with defaults to ensure all keys exist
                    self.settings = self._merge_settings(self.default_settings, loaded_settings)
                    
                    # Update last_updated timestamp
                    self.settings['last_updated'] = datetime.now().isoformat()
                    
                    logger.info("User settings loaded from file")
                    return True
                else:
                    # Create default settings file
                    self.settings = self.default_settings.copy()
                    self.save_settings()
                    logger.info("Default user settings created")
                    return False
                    
            except Exception as e:
                logger.error(f"Error loading user settings: {e}")
                logger.info("Using default settings")
                self.settings = self.default_settings.copy()
                return False

    def reload_settings(self) -> bool:
        """
        Reload settings from file (alias for load_settings).
        Useful for forcing refresh when settings have been updated externally.

        Returns:
            True if loaded from file, False if using defaults
        """
        return self.load_settings()
    
    def save_settings(self) -> bool:
        """
        Save current settings to file.
        
        Returns:
            True if successful, False otherwise
        """
        with self._lock:
            try:
                # Update timestamp
                self.settings['last_updated'] = datetime.now().isoformat()
                
                # Write to file with pretty formatting
                with open(self.settings_file, 'w', encoding='utf-8') as f:
                    json.dump(self.settings, f, indent=2, ensure_ascii=False)
                
                logger.info("User settings saved successfully")
                return True
                
            except Exception as e:
                logger.error(f"Error saving user settings: {e}")
                return False
    
    def _merge_settings(self, defaults: Dict[str, Any], loaded: Dict[str, Any]) -> Dict[str, Any]:
        """
        Merge loaded settings with defaults to ensure all keys exist.
        
        Args:
            defaults: Default settings dictionary
            loaded: Loaded settings dictionary
            
        Returns:
            Merged settings dictionary
        """
        merged = defaults.copy()
        
        for key, value in loaded.items():
            if key in merged:
                if isinstance(value, dict) and isinstance(merged[key], dict):
                    merged[key] = self._merge_settings(merged[key], value)
                else:
                    merged[key] = value
            else:
                merged[key] = value
        
        return merged
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        Get setting value using dot notation.
        
        Args:
            key_path: Dot-separated key path (e.g., 'timezone.data_timezone')
            default: Default value if key not found
            
        Returns:
            Setting value or default
        """
        try:
            keys = key_path.split('.')
            value = self.settings
            
            for key in keys:
                value = value[key]
            
            return value
            
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any) -> bool:
        """
        Set setting value using dot notation.
        
        Args:
            key_path: Dot-separated key path (e.g., 'timezone.data_timezone')
            value: Value to set
            
        Returns:
            True if successful, False otherwise
        """
        try:
            keys = key_path.split('.')
            target = self.settings
            
            # Navigate to parent of target key
            for key in keys[:-1]:
                if key not in target:
                    target[key] = {}
                target = target[key]
            
            # Set the value
            target[keys[-1]] = value
            
            return True
            
        except Exception as e:
            logger.error(f"Error setting {key_path} = {value}: {e}")
            return False
    
    def get_timezone_settings(self) -> Dict[str, str]:
        """Get timezone configuration."""
        return self.get('timezone', {})
    
    def get_market_session_settings(self) -> Dict[str, Any]:
        """Get market session configuration."""
        return self.get('market_sessions', {})
    
    def get_data_loading_settings(self) -> Dict[str, Any]:
        """Get data loading configuration."""
        return self.get('data_loading', {})
    
    def get_cache_settings(self) -> Dict[str, Any]:
        """Get cache configuration."""
        return self.get('cache', {})
    
    def get_ui_preferences(self) -> Dict[str, Any]:
        """Get UI preferences."""
        return self.get('ui_preferences', {})
    
    def is_forex_pair(self, pair: str) -> bool:
        """
        Check if pair is forex (not in non-forex symbols list).
        
        Args:
            pair: Currency pair symbol
            
        Returns:
            True if forex, False if non-forex
        """
        non_forex_symbols = self.get('market_sessions.non_forex_symbols', [])
        return pair.upper() not in [symbol.upper() for symbol in non_forex_symbols]
    
    def get_market_open_time(self, pair: str) -> str:
        """
        Get market open time for specific pair.
        
        Args:
            pair: Currency pair symbol
            
        Returns:
            Market open time string (e.g., '16:00')
        """
        if self.is_forex_pair(pair):
            return self.get('market_sessions.forex_open', '16:00')
        else:
            return self.get('market_sessions.non_forex_open', '17:00')
    
    def get_enabled_timeframes(self) -> List[str]:
        """Get list of enabled timeframes."""
        return self.get('data_loading.enabled_timeframes', ['M1', 'M5', 'M15', 'H1', 'H4', 'D1'])
    
    def is_timeframe_enabled(self, timeframe: str) -> bool:
        """Check if timeframe is enabled."""
        enabled_tfs = self.get_enabled_timeframes()
        return timeframe in enabled_tfs
    
    def reset_to_defaults(self) -> bool:
        """
        Reset all settings to defaults.
        
        Returns:
            True if successful, False otherwise
        """
        with self._lock:
            self.settings = self.default_settings.copy()
            return self.save_settings()
    
    def export_settings(self, filepath: Optional[str] = None) -> bool:
        """
        Export settings to external file.
        
        Args:
            filepath: Target file path (optional)
            
        Returns:
            True if successful, False otherwise
        """
        if filepath is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filepath = self.config_dir / f'user_settings_backup_{timestamp}.json'
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Settings exported to: {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting settings: {e}")
            return False
    
    def import_settings(self, filepath: str) -> bool:
        """
        Import settings from external file.
        
        Args:
            filepath: Source file path
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                imported_settings = json.load(f)
            
            # Merge with defaults to ensure all keys exist
            self.settings = self._merge_settings(self.default_settings, imported_settings)
            
            # Save the imported settings
            success = self.save_settings()
            
            if success:
                logger.info(f"Settings imported from: {filepath}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error importing settings: {e}")
            return False


# Global user settings instance (lazy initialization to avoid startup issues)
user_settings = None

def get_user_settings():
    """Get user settings instance with lazy initialization."""
    global user_settings
    if user_settings is None:
        try:
            user_settings = UserSettings()
        except Exception as e:
            print(f"Warning: Could not initialize user settings: {e}")
            # Return minimal settings for compatibility
            from .minimal_settings import MinimalSettings
            user_settings = MinimalSettings()
    return user_settings
