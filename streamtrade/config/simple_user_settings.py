"""
Simple User Settings System for Lionaire Platform (without logging dependencies).
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime
import threading


class SimpleUserSettings:
    """
    Simple user settings management with project-local storage.
    This version doesn't use logging to avoid circular imports during testing.
    """
    
    def __init__(self):
        # Use project-local config directory
        self.project_dir = Path(__file__).parent.parent
        self.config_dir = self.project_dir / 'config'
        self.settings_file = self.config_dir / 'user_settings.json'
        
        # Ensure config directory exists
        self.config_dir.mkdir(exist_ok=True)
        
        # Thread safety
        self._lock = threading.Lock()
        
        # Default settings
        self.default_settings = {
            'timezone': {
                'data_timezone': 'UTC-5',  # EST without DST
                'display_timezone': 'UTC+7'  # Asia/Jakarta
            },
            'market_sessions': {
                'forex_open': '16:00',  # Forex market open time
                'non_forex_open': '17:00',  # Non-forex market open time
                'non_forex_symbols': ['XAUUSD', 'SPXUSD', 'NSXUSD']
            },
            'data_loading': {
                'days_back_default': 5,  # Default days to load back
                'days_back_minimum': 1,  # Minimum days allowed
                'max_candles_load': 200000,  # Max candles to load from files
                'max_candles_display': 15000,  # Max candles to display on chart
                'enabled_timeframes': ['M1', 'M5', 'M15', 'H1', 'H4', 'D1'],  # Default enabled TFs
                'disabled_timeframes': ['M30', 'W1', 'MN1'],  # Default disabled TFs
                'cache_all_tf_on_load': False  # Cache all timeframes on initial load
            },
            'cache': {
                'max_cache_size_gb': 10,  # Maximum cache size in GB
                'insufficient_data_behavior': 'show_warning',  # 'show_warning' or 'auto_load'
                'enable_disk_cache': True,  # Enable disk-based caching
                'cache_compression': 'snappy'  # Parquet compression method
            },
            'ui_preferences': {
                'default_timeframe': 'H1',
                'chart_style': 'candlestick',  # 'candlestick', 'ohlc', 'line'
                'remove_gaps': True,  # Remove weekend gaps by default
                'crosshair_enabled': True,
                'crosshair_vertical': True,
                'crosshair_horizontal': True
            },
            'version': '1.0.0',
            'last_updated': datetime.now().isoformat()
        }
        
        # Current settings (loaded from file or defaults)
        self.settings = {}
        
        # Load existing settings or create defaults
        self.load_settings()
    
    def load_settings(self) -> bool:
        """
        Load settings from file or create defaults.
        
        Returns:
            True if loaded from file, False if created defaults
        """
        with self._lock:
            try:
                if self.settings_file.exists():
                    with open(self.settings_file, 'r', encoding='utf-8') as f:
                        loaded_settings = json.load(f)
                    
                    # Merge with defaults to ensure all keys exist
                    self.settings = self._merge_settings(self.default_settings, loaded_settings)
                    
                    # Update last_updated timestamp
                    self.settings['last_updated'] = datetime.now().isoformat()
                    
                    return True
                else:
                    # Create default settings file
                    self.settings = self.default_settings.copy()
                    self.save_settings()
                    return False
                    
            except Exception as e:
                print(f"Error loading user settings: {e}")
                self.settings = self.default_settings.copy()
                return False
    
    def save_settings(self) -> bool:
        """
        Save current settings to file.
        
        Returns:
            True if successful, False otherwise
        """
        with self._lock:
            try:
                # Update timestamp
                self.settings['last_updated'] = datetime.now().isoformat()
                
                # Write to file with pretty formatting
                with open(self.settings_file, 'w', encoding='utf-8') as f:
                    json.dump(self.settings, f, indent=2, ensure_ascii=False)
                
                return True
                
            except Exception as e:
                print(f"Error saving user settings: {e}")
                return False
    
    def _merge_settings(self, defaults: Dict[str, Any], loaded: Dict[str, Any]) -> Dict[str, Any]:
        """
        Merge loaded settings with defaults to ensure all keys exist.
        """
        merged = defaults.copy()
        
        for key, value in loaded.items():
            if key in merged:
                if isinstance(value, dict) and isinstance(merged[key], dict):
                    merged[key] = self._merge_settings(merged[key], value)
                else:
                    merged[key] = value
            else:
                merged[key] = value
        
        return merged
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        Get setting value using dot notation.
        """
        try:
            keys = key_path.split('.')
            value = self.settings
            
            for key in keys:
                value = value[key]
            
            return value
            
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any) -> bool:
        """
        Set setting value using dot notation.
        """
        try:
            keys = key_path.split('.')
            target = self.settings
            
            # Navigate to parent of target key
            for key in keys[:-1]:
                if key not in target:
                    target[key] = {}
                target = target[key]
            
            # Set the value
            target[keys[-1]] = value
            
            return True
            
        except Exception as e:
            print(f"Error setting {key_path} = {value}: {e}")
            return False
    
    def is_forex_pair(self, pair: str) -> bool:
        """
        Check if pair is forex (not in non-forex symbols list).
        """
        non_forex_symbols = self.get('market_sessions.non_forex_symbols', [])
        return pair.upper() not in [symbol.upper() for symbol in non_forex_symbols]
    
    def get_market_open_time(self, pair: str) -> str:
        """
        Get market open time for specific pair.
        """
        if self.is_forex_pair(pair):
            return self.get('market_sessions.forex_open', '16:00')
        else:
            return self.get('market_sessions.non_forex_open', '17:00')
    
    def get_enabled_timeframes(self) -> List[str]:
        """Get list of enabled timeframes."""
        return self.get('data_loading.enabled_timeframes', ['M1', 'M5', 'M15', 'H1', 'H4', 'D1'])
    
    def is_timeframe_enabled(self, timeframe: str) -> bool:
        """Check if timeframe is enabled."""
        enabled_tfs = self.get_enabled_timeframes()
        return timeframe in enabled_tfs


# Test the simple version
if __name__ == "__main__":
    print("Testing SimpleUserSettings...")
    
    settings = SimpleUserSettings()
    print(f"✓ Settings created")
    print(f"Data timezone: {settings.get('timezone.data_timezone')}")
    print(f"Forex open: {settings.get('market_sessions.forex_open')}")
    print(f"Is EURUSD forex: {settings.is_forex_pair('EURUSD')}")
    print(f"Is XAUUSD forex: {settings.is_forex_pair('XAUUSD')}")
    print(f"EURUSD market open: {settings.get_market_open_time('EURUSD')}")
    print(f"XAUUSD market open: {settings.get_market_open_time('XAUUSD')}")
    print("✅ All tests passed!")
