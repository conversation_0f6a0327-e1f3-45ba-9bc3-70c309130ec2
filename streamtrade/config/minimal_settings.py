"""
Minimal settings for testing Phase 5.1 implementation.
"""

import json
from pathlib import Path


class MinimalSettings:
    """Minimal settings class for testing."""
    
    def __init__(self):
        self.settings = {
            'timezone': {
                'data_timezone': 'UTC-5',
                'display_timezone': 'UTC+7'
            },
            'market_sessions': {
                'forex_open': '16:00',
                'non_forex_open': '17:00',
                'non_forex_symbols': ['XAUUSD', 'SPXUSD', 'NSXUSD']
            },
            'data_loading': {
                'days_back_default': 5,
                'max_candles_load': 200000,
                'max_candles_display': 15000,
                'enabled_timeframes': ['M1', 'M5', 'M15', 'H1', 'H4', 'D1']
            }
        }
    
    def get(self, key_path: str, default=None):
        """Get setting using dot notation."""
        try:
            keys = key_path.split('.')
            value = self.settings
            for key in keys:
                value = value[key]
            return value
        except (<PERSON><PERSON><PERSON><PERSON>, TypeError):
            return default
    
    def is_forex_pair(self, pair: str) -> bool:
        """Check if pair is forex."""
        non_forex = self.get('market_sessions.non_forex_symbols', [])
        return pair.upper() not in [s.upper() for s in non_forex]
    
    def get_market_open_time(self, pair: str) -> str:
        """Get market open time for pair."""
        if self.is_forex_pair(pair):
            return self.get('market_sessions.forex_open', '16:00')
        else:
            return self.get('market_sessions.non_forex_open', '17:00')


# Test
if __name__ == "__main__":
    print("Testing MinimalSettings...")
    settings = MinimalSettings()
    print(f"✓ Settings created")
    print(f"Data timezone: {settings.get('timezone.data_timezone')}")
    print(f"Is EURUSD forex: {settings.is_forex_pair('EURUSD')}")
    print(f"Is XAUUSD forex: {settings.is_forex_pair('XAUUSD')}")
    print("✅ Test completed!")
