"""
Smart Disk Cache System for Lionaire platform.
Provides efficient Parquet-based caching with LRU eviction.
"""

import os
import json
import hashlib
import threading
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple
import pandas as pd

from ..config.logging_config import get_logger

logger = get_logger(__name__)


class SmartDiskCache:
    """
    Smart Disk Cache System with Parquet storage and LRU eviction.
    """
    
    def __init__(self, cache_dir: Optional[Path] = None, max_size_gb: int = 10, compression: str = 'snappy'):
        """
        Initialize Smart Disk Cache.
        
        Args:
            cache_dir: Cache directory path (default: ./cache)
            max_size_gb: Maximum cache size in GB
            compression: Parquet compression method
        """
        # Use project-local cache directory
        if cache_dir is None:
            cache_dir = Path(__file__).parent.parent / 'cache'
        
        self.cache_dir = Path(cache_dir)
        self.max_size_gb = max_size_gb
        self.compression = compression
        
        # Create directory structure
        self._create_directory_structure()
        
        # Thread safety
        self._lock = threading.RLock()
        
        logger.info(f"Smart Disk Cache initialized: {self.cache_dir} (max: {max_size_gb} GB)")
    
    def _create_directory_structure(self):
        """Create cache directory structure."""
        try:
            # Main directories
            (self.cache_dir / 'data' / 'm1_base').mkdir(parents=True, exist_ok=True)
            (self.cache_dir / 'data' / 'timeframes').mkdir(parents=True, exist_ok=True)
            (self.cache_dir / 'data' / 'indicators').mkdir(parents=True, exist_ok=True)
            (self.cache_dir / 'metadata').mkdir(parents=True, exist_ok=True)
            (self.cache_dir / 'styles').mkdir(parents=True, exist_ok=True)
            
            logger.debug(f"Cache directory structure created: {self.cache_dir}")
            
        except Exception as e:
            logger.error(f"Error creating cache directory structure: {e}")
            raise
    
    def _generate_cache_key(self, *args) -> str:
        """Generate cache key from arguments."""
        key_string = '_'.join(str(arg) for arg in args)
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def store_data(self, cache_key: str, data: pd.DataFrame, cache_type: str = 'timeframes') -> bool:
        """
        Store data in cache.
        
        Args:
            cache_key: Unique cache key
            data: DataFrame to store
            cache_type: Type of cache ('m1_base', 'timeframes', 'indicators')
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with self._lock:
                # Determine file path
                file_path = self.cache_dir / 'data' / cache_type / f"{cache_key}.parquet"
                
                # Store data as Parquet
                data.to_parquet(file_path, compression=self.compression)
                
                # Update metadata
                self._update_metadata(cache_key, file_path, cache_type)
                
                logger.debug(f"Data stored in cache: {cache_key} ({cache_type})")
                return True
                
        except Exception as e:
            logger.error(f"Error storing data in cache: {e}")
            return False
    
    def load_data(self, cache_key: str, cache_type: str = 'timeframes') -> Optional[pd.DataFrame]:
        """
        Load data from cache.
        
        Args:
            cache_key: Cache key to load
            cache_type: Type of cache
            
        Returns:
            DataFrame if found, None otherwise
        """
        try:
            with self._lock:
                file_path = self.cache_dir / 'data' / cache_type / f"{cache_key}.parquet"
                
                if file_path.exists():
                    data = pd.read_parquet(file_path)
                    logger.debug(f"Data loaded from cache: {cache_key} ({cache_type})")
                    return data
                else:
                    return None
                    
        except Exception as e:
            logger.error(f"Error loading data from cache: {e}")
            return None
    
    def _update_metadata(self, cache_key: str, file_path: Path, cache_type: str):
        """Update cache metadata."""
        try:
            metadata_file = self.cache_dir / 'metadata' / 'index.json'
            
            # Load existing metadata
            if metadata_file.exists():
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
            else:
                metadata = {}
            
            # Update metadata
            metadata[cache_key] = {
                'file_path': str(file_path),
                'cache_type': cache_type,
                'created': datetime.now().isoformat(),
                'accessed': datetime.now().isoformat(),
                'size_bytes': file_path.stat().st_size if file_path.exists() else 0
            }
            
            # Save metadata
            with open(metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error updating metadata: {e}")
    
    def find_m1_cache(self, pair: str, start_date: datetime, end_date: datetime) -> Optional[str]:
        """Find M1 cache key for given parameters."""
        cache_key = self._generate_cache_key('m1', pair, start_date.date(), end_date.date())
        
        if self.load_data(cache_key, 'm1_base') is not None:
            return cache_key
        return None
    
    def find_timeframe_cache(self, pair: str, timeframe: str, m1_cache_key: str) -> Optional[str]:
        """Find timeframe cache key."""
        cache_key = self._generate_cache_key('tf', pair, timeframe, m1_cache_key)
        
        if self.load_data(cache_key, 'timeframes') is not None:
            return cache_key
        return None
    
    def store_m1_data(self, pair: str, data: pd.DataFrame, start_date: datetime, end_date: datetime) -> str:
        """Store M1 base data."""
        cache_key = self._generate_cache_key('m1', pair, start_date.date(), end_date.date())
        
        if self.store_data(cache_key, data, 'm1_base'):
            return cache_key
        return None
    
    def store_timeframe_data(self, pair: str, timeframe: str, data: pd.DataFrame, m1_cache_key: str) -> str:
        """Store timeframe data."""
        cache_key = self._generate_cache_key('tf', pair, timeframe, m1_cache_key)
        
        if self.store_data(cache_key, data, 'timeframes'):
            return cache_key
        return None
    
    def clear_cache(self) -> bool:
        """Clear all cache data."""
        try:
            with self._lock:
                # Remove all data files
                for cache_type in ['m1_base', 'timeframes', 'indicators']:
                    cache_dir = self.cache_dir / 'data' / cache_type
                    if cache_dir.exists():
                        for file_path in cache_dir.glob('*.parquet'):
                            file_path.unlink()
                
                # Clear metadata
                metadata_file = self.cache_dir / 'metadata' / 'index.json'
                if metadata_file.exists():
                    metadata_file.unlink()
                
                logger.info("Cache cleared successfully")
                return True
                
        except Exception as e:
            logger.error(f"Error clearing cache: {e}")
            return False
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get simplified cache statistics for UI display."""
        try:
            metadata_file = self.cache_dir / 'metadata' / 'index.json'
            
            if metadata_file.exists():
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                
                total_entries = len(metadata)
                total_size_bytes = sum(entry.get('size_bytes', 0) for entry in metadata.values())
                total_size_mb = total_size_bytes / (1024 * 1024)
                
                entries_by_type = {}
                for entry in metadata.values():
                    cache_type = entry.get('cache_type', 'unknown')
                    entries_by_type[cache_type] = entries_by_type.get(cache_type, 0) + 1
                
                return {
                    'total_entries': total_entries,
                    'total_size_mb': total_size_mb,
                    'entries_by_type': entries_by_type,
                    'cache_dir': str(self.cache_dir)
                }
            else:
                return {
                    'total_entries': 0,
                    'total_size_mb': 0.0,
                    'entries_by_type': {},
                    'cache_dir': str(self.cache_dir)
                }
                
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {
                'total_entries': 0,
                'total_size_mb': 0.0,
                'entries_by_type': {},
                'cache_dir': str(self.cache_dir)
            }
    
    def clear_all_cache(self) -> bool:
        """Clear all cache data and reset cache directory."""
        return self.clear_cache()

    def get_stats(self) -> Dict[str, Any]:
        """Alias for get_cache_stats for compatibility."""
        return self.get_cache_stats()
