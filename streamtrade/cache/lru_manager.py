"""
LRU Cache Manager for Smart Disk Cache System.
Handles Least Recently Used eviction logic.
"""

import json
import threading
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List

from ..config.logging_config import get_logger

logger = get_logger(__name__)


class LRUCacheManager:
    """
    LRU (Least Recently Used) Cache Manager.
    Tracks access times and handles eviction when cache size exceeds limits.
    """
    
    def __init__(self, cache_dir: Path, max_size_gb: int = 10):
        """
        Initialize LRU Cache Manager.
        
        Args:
            cache_dir: Cache directory path
            max_size_gb: Maximum cache size in GB
        """
        self.cache_dir = Path(cache_dir)
        self.max_size_gb = max_size_gb
        self.lru_file = self.cache_dir / 'metadata' / 'lru_tracker.json'
        
        # Thread safety
        self._lock = threading.RLock()
        
        # Access log
        self.access_log = self._load_access_log()
        
        logger.debug(f"LRU Cache Manager initialized: {cache_dir} (max: {max_size_gb} GB)")
    
    def _load_access_log(self) -> Dict[str, datetime]:
        """Load access log from file."""
        try:
            if self.lru_file.exists():
                with open(self.lru_file, 'r') as f:
                    data = json.load(f)
                    # Convert ISO strings back to datetime objects
                    return {key: datetime.fromisoformat(value) for key, value in data.items()}
            else:
                return {}
        except Exception as e:
            logger.error(f"Error loading access log: {e}")
            return {}
    
    def _save_access_log(self):
        """Save access log to file."""
        try:
            with self._lock:
                # Ensure directory exists
                self.lru_file.parent.mkdir(parents=True, exist_ok=True)
                
                # Convert datetime objects to ISO strings
                data = {key: value.isoformat() for key, value in self.access_log.items()}
                
                with open(self.lru_file, 'w') as f:
                    json.dump(data, f, indent=2)
                    
        except Exception as e:
            logger.error(f"Error saving access log: {e}")
    
    def record_access(self, cache_key: str):
        """Record access time for cache key."""
        try:
            with self._lock:
                self.access_log[cache_key] = datetime.now()
                self._save_access_log()
                
        except Exception as e:
            logger.error(f"Error recording access: {e}")
    
    def get_lru_keys(self, count: int) -> List[str]:
        """Get least recently used cache keys."""
        try:
            # Sort by access time (oldest first)
            sorted_keys = sorted(self.access_log.items(), key=lambda x: x[1])
            return [key for key, _ in sorted_keys[:count]]
            
        except Exception as e:
            logger.error(f"Error getting LRU keys: {e}")
            return []
    
    def remove_key(self, cache_key: str):
        """Remove key from access log."""
        try:
            with self._lock:
                if cache_key in self.access_log:
                    del self.access_log[cache_key]
                    self._save_access_log()
                    
        except Exception as e:
            logger.error(f"Error removing key: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get LRU manager statistics."""
        try:
            return {
                'max_size_gb': self.max_size_gb,
                'tracked_keys': len(self.access_log),
                'eviction_stats': {
                    'total_evictions': 0,  # Placeholder
                    'last_eviction': None  # Placeholder
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting stats: {e}")
            return {
                'max_size_gb': self.max_size_gb,
                'tracked_keys': 0,
                'eviction_stats': {
                    'total_evictions': 0,
                    'last_eviction': None
                }
            }
