"""
Indicator Cache System for Smart Disk Cache.
Handles per-indicator caching with style separation.
"""

import json
import hashlib
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional, List
import pandas as pd

from ..config.logging_config import get_logger

logger = get_logger(__name__)


class IndicatorCache:
    """
    Indicator Cache System.
    Provides per-indicator caching with style data separation.
    """
    
    def __init__(self, disk_cache):
        """
        Initialize Indicator Cache.
        
        Args:
            disk_cache: SmartDiskCache instance
        """
        self.disk_cache = disk_cache
        self.cache_dir = disk_cache.cache_dir
        
        logger.debug("Indicator Cache initialized")
    
    def _generate_indicator_key(self, pair: str, timeframe: str, indicator_name: str, 
                              params: Dict[str, Any], data_cache_key: str) -> str:
        """Generate cache key for indicator data."""
        # Create deterministic key from parameters
        params_str = json.dumps(params, sort_keys=True)
        key_string = f"indicator_{pair}_{timeframe}_{indicator_name}_{params_str}_{data_cache_key}"
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def _generate_style_key(self, indicator_cache_key: str) -> str:
        """Generate cache key for indicator style."""
        return f"style_{indicator_cache_key}"
    
    def store_indicator_data(self, pair: str, timeframe: str, indicator_name: str,
                           params: Dict[str, Any], data: pd.DataFrame, 
                           data_cache_key: str) -> Optional[str]:
        """
        Store indicator calculation data.
        
        Args:
            pair: Currency pair
            timeframe: Timeframe
            indicator_name: Name of indicator
            params: Indicator parameters
            data: Calculated indicator data
            data_cache_key: Key of source data
            
        Returns:
            Cache key if successful, None otherwise
        """
        try:
            cache_key = self._generate_indicator_key(
                pair, timeframe, indicator_name, params, data_cache_key
            )
            
            if self.disk_cache.store_data(cache_key, data, 'indicators'):
                logger.debug(f"Indicator data stored: {indicator_name} for {pair} {timeframe}")
                return cache_key
            else:
                return None
                
        except Exception as e:
            logger.error(f"Error storing indicator data: {e}")
            return None
    
    def load_indicator_data(self, pair: str, timeframe: str, indicator_name: str,
                          params: Dict[str, Any], data_cache_key: str) -> Optional[pd.DataFrame]:
        """
        Load indicator calculation data.
        
        Args:
            pair: Currency pair
            timeframe: Timeframe
            indicator_name: Name of indicator
            params: Indicator parameters
            data_cache_key: Key of source data
            
        Returns:
            Indicator data if found, None otherwise
        """
        try:
            cache_key = self._generate_indicator_key(
                pair, timeframe, indicator_name, params, data_cache_key
            )
            
            data = self.disk_cache.load_data(cache_key, 'indicators')
            if data is not None:
                logger.debug(f"Indicator data loaded: {indicator_name} for {pair} {timeframe}")
            
            return data
            
        except Exception as e:
            logger.error(f"Error loading indicator data: {e}")
            return None
    
    def store_indicator_style(self, indicator_cache_key: str, style_config: Dict[str, Any]) -> bool:
        """
        Store indicator style configuration.
        
        Args:
            indicator_cache_key: Indicator cache key
            style_config: Style configuration
            
        Returns:
            True if successful, False otherwise
        """
        try:
            style_key = self._generate_style_key(indicator_cache_key)
            style_file = self.cache_dir / 'styles' / f"{style_key}.json"
            
            # Ensure directory exists
            style_file.parent.mkdir(parents=True, exist_ok=True)
            
            # Store style as JSON
            with open(style_file, 'w') as f:
                json.dump(style_config, f, indent=2)
            
            logger.debug(f"Indicator style stored: {style_key}")
            return True
            
        except Exception as e:
            logger.error(f"Error storing indicator style: {e}")
            return False
    
    def load_indicator_style(self, indicator_cache_key: str) -> Optional[Dict[str, Any]]:
        """
        Load indicator style configuration.
        
        Args:
            indicator_cache_key: Indicator cache key
            
        Returns:
            Style configuration if found, None otherwise
        """
        try:
            style_key = self._generate_style_key(indicator_cache_key)
            style_file = self.cache_dir / 'styles' / f"{style_key}.json"
            
            if style_file.exists():
                with open(style_file, 'r') as f:
                    style_config = json.load(f)
                
                logger.debug(f"Indicator style loaded: {style_key}")
                return style_config
            else:
                return None
                
        except Exception as e:
            logger.error(f"Error loading indicator style: {e}")
            return None
    
    def clear_indicator_cache(self, pair: str = None, timeframe: str = None, 
                            indicator_name: str = None) -> bool:
        """
        Clear indicator cache entries.
        
        Args:
            pair: Currency pair (optional filter)
            timeframe: Timeframe (optional filter)
            indicator_name: Indicator name (optional filter)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # This is a simplified implementation
            # In a full implementation, we would filter by the provided parameters
            indicators_dir = self.cache_dir / 'data' / 'indicators'
            styles_dir = self.cache_dir / 'styles'
            
            if indicators_dir.exists():
                for file_path in indicators_dir.glob('*.parquet'):
                    file_path.unlink()
            
            if styles_dir.exists():
                for file_path in styles_dir.glob('*.json'):
                    file_path.unlink()
            
            logger.info("Indicator cache cleared")
            return True
            
        except Exception as e:
            logger.error(f"Error clearing indicator cache: {e}")
            return False
