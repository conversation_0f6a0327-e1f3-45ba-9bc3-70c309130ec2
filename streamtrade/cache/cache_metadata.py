"""
Cache Metadata Manager for Smart Disk Cache System.
Handles metadata tracking and dependency management.
"""

import json
import threading
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List, Optional

from ..config.logging_config import get_logger

logger = get_logger(__name__)


class CacheMetadata:
    """
    Cache Metadata Manager.
    Tracks cache entries, dependencies, and metadata.
    """
    
    def __init__(self, metadata_dir: Path):
        """
        Initialize Cache Metadata Manager.
        
        Args:
            metadata_dir: Metadata directory path
        """
        self.metadata_dir = Path(metadata_dir)
        self.metadata_file = self.metadata_dir / 'index.json'
        
        # Thread safety
        self._lock = threading.RLock()
        
        # Ensure directory exists
        self.metadata_dir.mkdir(parents=True, exist_ok=True)
        
        logger.debug(f"Cache Metadata Manager initialized: {metadata_dir}")
    
    def _load_metadata(self) -> Dict[str, Any]:
        """Load metadata from file."""
        try:
            if self.metadata_file.exists():
                with open(self.metadata_file, 'r') as f:
                    return json.load(f)
            else:
                return {}
        except Exception as e:
            logger.error(f"Error loading metadata: {e}")
            return {}
    
    def _save_metadata(self, metadata: Dict[str, Any]):
        """Save metadata to file."""
        try:
            with self._lock:
                with open(self.metadata_file, 'w') as f:
                    json.dump(metadata, f, indent=2)
                    
        except Exception as e:
            logger.error(f"Error saving metadata: {e}")
    
    def add_entry(self, cache_key: str, entry_data: Dict[str, Any]):
        """Add cache entry to metadata."""
        try:
            with self._lock:
                metadata = self._load_metadata()
                metadata[cache_key] = entry_data
                self._save_metadata(metadata)
                
        except Exception as e:
            logger.error(f"Error adding entry: {e}")
    
    def remove_entry(self, cache_key: str):
        """Remove cache entry from metadata."""
        try:
            with self._lock:
                metadata = self._load_metadata()
                if cache_key in metadata:
                    del metadata[cache_key]
                    self._save_metadata(metadata)
                    
        except Exception as e:
            logger.error(f"Error removing entry: {e}")
    
    def get_entry(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get cache entry metadata."""
        try:
            metadata = self._load_metadata()
            return metadata.get(cache_key)
            
        except Exception as e:
            logger.error(f"Error getting entry: {e}")
            return None
    
    def find_dependents(self, cache_key: str) -> List[str]:
        """Find cache entries that depend on given key."""
        try:
            metadata = self._load_metadata()
            dependents = []
            
            for key, entry in metadata.items():
                if entry.get('depends_on') == cache_key:
                    dependents.append(key)
            
            return dependents
            
        except Exception as e:
            logger.error(f"Error finding dependents: {e}")
            return []
    
    def get_stats(self) -> Dict[str, Any]:
        """Get metadata statistics."""
        try:
            metadata = self._load_metadata()
            
            total_entries = len(metadata)
            total_size_bytes = sum(entry.get('size_bytes', 0) for entry in metadata.values())
            total_size_mb = total_size_bytes / (1024 * 1024)
            
            entries_by_type = {}
            for entry in metadata.values():
                cache_type = entry.get('cache_type', 'unknown')
                entries_by_type[cache_type] = entries_by_type.get(cache_type, 0) + 1
            
            return {
                'total_entries': total_entries,
                'total_size_mb': total_size_mb,
                'entries_by_type': entries_by_type
            }
            
        except Exception as e:
            logger.error(f"Error getting stats: {e}")
            return {
                'total_entries': 0,
                'total_size_mb': 0.0,
                'entries_by_type': {}
            }
