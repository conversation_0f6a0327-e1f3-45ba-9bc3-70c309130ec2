"""
Streamlit app launcher for StreamTrade platform.
Run with: streamlit run run_streamlit.py
"""

import sys
from pathlib import Path

# Add current directory to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Import and run the main app
try:
    from streamtrade.gui.main_app import main
    # Run the main app
    main()
except ImportError as e:
    print(f"Import error: {e}")
    print("Please run from streamtrade directory: cd streamtrade && streamlit run gui/main_app.py")
